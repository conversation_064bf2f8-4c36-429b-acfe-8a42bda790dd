#!/usr/bin/env python3
"""
Test script for English-only extraction
"""

def test_pan_english_only():
    """Test PAN card with English text only"""
    print("Testing PAN Card (English only)...")
    
    sample_text = """INCOME TAX DEPARTMENT
GOVT. OF INDIA
PERMANENT ACCOUNT NUMBER

**********

Name: RAJESH KUMAR SHARMA
Father's Name: MOHAN LAL SHARMA
Date of Birth: 15/08/1985"""
    
    try:
        from document_processor import DocumentProcessor
        
        processor = DocumentProcessor()
        extracted_data = processor.extract_pan_data(sample_text)
        
        print("Extracted Data:")
        for key, value in extracted_data.items():
            print(f"  {key}: {value}")
        
        # Check key fields
        checks = [
            ('PAN Number', '**********'),
            ('Name', 'RAJESH KUMAR SHARMA'),
            ('Father\'s Name', 'MOHAN LAL SHARMA'),
            ('Date of Birth', '15/08/1985')
        ]
        
        passed = 0
        for field, expected in checks:
            actual = extracted_data.get(field, '')
            if expected == actual:
                print(f"✓ {field}: CORRECT")
                passed += 1
            else:
                print(f"✗ {field}: Expected '{expected}', got '{actual}'")
        
        return passed >= 3
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def test_aadhaar_english_only():
    """Test Aadhaar card with English text only"""
    print("\nTesting Aadhaar Card (English only)...")
    
    sample_text = """GOVERNMENT OF INDIA
UNIQUE IDENTIFICATION AUTHORITY OF INDIA
AADHAAR

Prakash Gopichand Rathod

**************

DOB: 01/07/1974
Gender: Male

Address: S/O Gopichand Rathod, Ward No-14, Ramnagar,
Pune, Maharashtra, 411006"""
    
    try:
        from document_processor import DocumentProcessor
        
        processor = DocumentProcessor()
        extracted_data = processor.extract_aadhaar_data(sample_text)
        
        print("Extracted Data:")
        for key, value in extracted_data.items():
            print(f"  {key}: {value}")
        
        # Check key fields
        checks = [
            ('Name', 'Prakash Gopichand Rathod'),
            ('Aadhaar Number', '**************'),
            ('Date of Birth', '01/07/1974'),
            ('Gender', 'Male')
        ]
        
        passed = 0
        for field, expected in checks:
            actual = extracted_data.get(field, '')
            if expected == actual:
                print(f"✓ {field}: CORRECT")
                passed += 1
            else:
                print(f"✗ {field}: Expected '{expected}', got '{actual}'")
        
        return passed >= 3
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def test_document_type_detection():
    """Test document type detection with English keywords"""
    print("\nTesting Document Type Detection...")
    
    test_cases = [
        ("INCOME TAX DEPARTMENT PERMANENT ACCOUNT NUMBER **********", "PAN Card"),
        ("GOVERNMENT OF INDIA AADHAAR 1234 5678 9012", "Aadhaar Card"),
        ("DRIVING LICENSE DL1234567890", "Driving License"),
    ]
    
    try:
        from document_types import DocumentTypeManager
        
        manager = DocumentTypeManager()
        passed = 0
        
        for text, expected_type in test_cases:
            doc_type, confidence = manager.identify_document_type(text)
            print(f"Text: {text[:50]}...")
            print(f"Detected: {doc_type} (confidence: {confidence:.2f})")
            print(f"Expected: {expected_type}")
            
            if doc_type == expected_type:
                print("✓ CORRECT")
                passed += 1
            else:
                print("✗ INCORRECT")
            print()
        
        return passed >= 2
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def main():
    print("=" * 60)
    print("Testing English-Only Document Extraction")
    print("=" * 60)
    
    tests = [
        test_pan_english_only,
        test_aadhaar_english_only,
        test_document_type_detection
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ PASSED")
            else:
                print("✗ FAILED")
        except Exception as e:
            print(f"✗ ERROR: {e}")
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{len(tests)} tests passed")
    
    if passed >= 2:
        print("🎉 English-only extraction is working!")
        print("\nKey features:")
        print("- Clean English text extraction")
        print("- Simplified pattern matching")
        print("- Consistent output format")
        print("- Better accuracy for English documents")
    else:
        print("⚠️  Some tests failed - check the patterns")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
