#!/usr/bin/env python3
"""
Test the enhanced extraction system with poor quality image handling
"""

import os
from document_processor import DocumentProcessor

def test_specific_images():
    """Test specific images mentioned by the user"""
    
    processor = DocumentProcessor()
    
    test_cases = [
        {
            'filename': 'ash-adhar.jpg',
            'expected_type': 'Aadhaar Card',
            'description': 'Aadhaar card that was previously unrecognized'
        },
        {
            'filename': 'shared_image_2.jpg', 
            'expected_type': 'Aadhaar Card',
            'description': 'Poor quality Aadhaar card'
        },
        {
            'filename': 'ash-pan.jpg',
            'expected_type': 'PAN Card',
            'description': 'Good quality PAN card (control test)'
        }
    ]
    
    print("🧪 Testing Enhanced Document Extraction")
    print("=" * 70)
    
    for test_case in test_cases:
        filename = test_case['filename']
        expected_type = test_case['expected_type']
        description = test_case['description']
        
        image_path = os.path.join('uploads', filename)
        
        if not os.path.exists(image_path):
            print(f"❌ {filename}: File not found")
            continue
        
        print(f"\n📄 Testing: {filename}")
        print(f"📝 Description: {description}")
        print(f"🎯 Expected: {expected_type}")
        print("-" * 50)
        
        try:
            # Extract text
            print("🔍 Extracting text...")
            text = processor.extract_text_from_image(image_path)
            text_length = len(text.strip())
            quality_score = processor._calculate_text_quality(text)
            
            print(f"📊 Text extracted: {text_length} characters")
            print(f"📊 Quality score: {quality_score:.2f}")
            
            # Show sample text
            if text:
                print(f"📄 Sample text (first 150 chars):")
                print(f"   '{text[:150]}...'")
            
            # Identify document type
            print(f"\n🏷️  Identifying document type...")
            doc_type = processor.identify_document_type(text)
            print(f"🏷️  Identified as: {doc_type}")
            
            # Check if identification improved
            if doc_type == expected_type:
                print(f"✅ SUCCESS: Correctly identified as {expected_type}")
            elif doc_type == 'Unknown':
                print(f"⚠️  Still unknown, checking suggestions...")
                suggestions = processor.get_document_suggestions(text)
                if suggestions['possible_types']:
                    top_suggestion = suggestions['possible_types'][0]
                    print(f"💡 Top suggestion: {top_suggestion['type']} (confidence: {top_suggestion['confidence']})")
                    if top_suggestion['type'] == expected_type:
                        print(f"✅ PARTIAL SUCCESS: Correct type in suggestions")
                    else:
                        print(f"❌ FAILED: Wrong suggestion")
                else:
                    print(f"❌ FAILED: No suggestions")
            else:
                print(f"❌ FAILED: Identified as {doc_type}, expected {expected_type}")
            
            # Extract structured data
            if doc_type != 'Unknown':
                print(f"\n📊 Extracting structured data...")
                structured_data = processor.extract_structured_data(text, doc_type)
                
                if structured_data:
                    print(f"✅ Extracted {len(structured_data)} fields:")
                    for key, value in structured_data.items():
                        if key == 'Quality_Remarks':
                            print(f"   ⚠️  {key}: {value}")
                        else:
                            print(f"   📋 {key}: {value}")
                    
                    # Check for quality remarks
                    if 'Quality_Remarks' in structured_data:
                        print(f"\n⚠️  Quality Issues Detected:")
                        for remark in structured_data['Quality_Remarks']:
                            print(f"   - {remark}")
                else:
                    print(f"❌ No structured data extracted")
            
        except Exception as e:
            print(f"❌ Error processing {filename}: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n" + "=" * 70)
    print(f"🏁 Testing Complete")

def test_poor_quality_techniques():
    """Test poor quality OCR techniques specifically"""
    
    processor = DocumentProcessor()
    
    print(f"\n🔬 Testing Poor Quality OCR Techniques")
    print("=" * 70)
    
    test_image = 'uploads/shared_image_2.jpg'
    
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        return
    
    print(f"📄 Testing with: {os.path.basename(test_image)}")
    
    # Test different preprocessing methods
    methods = [
        'standard',
        'aggressive', 
        'poor_quality',
        'extreme_enhancement',
        'noise_reduction'
    ]
    
    results = {}
    
    for method in methods:
        try:
            print(f"\n🔍 Testing method: {method}")
            
            # Process with specific method
            processed_img = processor.preprocess_image(test_image, method=method)
            
            # Extract text using this preprocessing
            from PIL import Image
            import pytesseract
            pil_img = Image.fromarray(processed_img)
            text = pytesseract.image_to_string(pil_img, lang='eng', config='--psm 6')
            
            text_length = len(text.strip())
            quality_score = processor._calculate_text_quality(text)
            
            results[method] = {
                'length': text_length,
                'quality': quality_score,
                'text': text[:100] + '...' if len(text) > 100 else text
            }
            
            print(f"   📊 Length: {text_length}, Quality: {quality_score:.2f}")
            
        except Exception as e:
            print(f"   ❌ Error with {method}: {e}")
            results[method] = {'error': str(e)}
    
    # Show best result
    print(f"\n🏆 Results Summary:")
    valid_results = {k: v for k, v in results.items() if 'error' not in v}
    
    if valid_results:
        best_method = max(valid_results.keys(), key=lambda k: valid_results[k]['length'] * valid_results[k]['quality'])
        print(f"🥇 Best method: {best_method}")
        print(f"   📊 Length: {valid_results[best_method]['length']}")
        print(f"   📊 Quality: {valid_results[best_method]['quality']:.2f}")
        print(f"   📄 Sample: {valid_results[best_method]['text']}")
    else:
        print(f"❌ No valid results")

def main():
    test_specific_images()
    test_poor_quality_techniques()

if __name__ == "__main__":
    main()
