#!/usr/bin/env python3
"""
Script to find and configure Tesseract OCR on Windows
"""

import os
import platform
import subprocess
import glob

def find_tesseract_windows():
    """Find Tesseract installation on Windows"""
    print("Searching for Tesseract OCR on Windows...")
    
    # Common installation paths
    common_paths = [
        r'C:\Program Files\Tesseract-OCR\tesseract.exe',
        r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
        r'C:\tesseract\tesseract.exe',
        r'C:\tools\tesseract\tesseract.exe'
    ]
    
    # Check user-specific paths
    username = os.environ.get('USERNAME', '')
    if username:
        user_paths = [
            rf'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe',
            rf'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'
        ]
        common_paths.extend(user_paths)
    
    found_paths = []
    
    print("\nChecking common installation paths:")
    for path in common_paths:
        print(f"  Checking: {path}")
        if os.path.exists(path):
            print(f"  ✓ Found: {path}")
            found_paths.append(path)
        else:
            print(f"  ✗ Not found")
    
    # Search in Program Files directories
    print("\nSearching in Program Files directories...")
    program_files_dirs = [
        r'C:\Program Files',
        r'C:\Program Files (x86)'
    ]
    
    for pf_dir in program_files_dirs:
        if os.path.exists(pf_dir):
            tesseract_pattern = os.path.join(pf_dir, '*tesseract*', 'tesseract.exe')
            matches = glob.glob(tesseract_pattern)
            for match in matches:
                if match not in found_paths:
                    print(f"  ✓ Found: {match}")
                    found_paths.append(match)
    
    return found_paths

def test_tesseract_path(tesseract_path):
    """Test if a Tesseract path works"""
    try:
        result = subprocess.run([tesseract_path, '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_info = result.stdout.strip().split('\n')[0]
            print(f"  ✓ Working: {version_info}")
            return True
        else:
            print(f"  ✗ Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"  ✗ Failed to run: {e}")
        return False

def update_document_processor(tesseract_path):
    """Update document_processor.py with the correct Tesseract path"""
    try:
        with open('document_processor.py', 'r') as f:
            content = f.read()
        
        # Find the line to replace
        lines = content.split('\n')
        updated_lines = []
        
        for line in lines:
            if 'pytesseract.pytesseract.tesseract_cmd = path' in line:
                # Replace with the correct path
                indent = len(line) - len(line.lstrip())
                new_line = ' ' * indent + f'pytesseract.pytesseract.tesseract_cmd = r"{tesseract_path}"'
                updated_lines.append(new_line)
                print(f"Updated line: {new_line}")
            else:
                updated_lines.append(line)
        
        # Write back to file
        with open('document_processor.py', 'w') as f:
            f.write('\n'.join(updated_lines))
        
        print("✓ Updated document_processor.py with Tesseract path")
        return True
        
    except Exception as e:
        print(f"✗ Failed to update document_processor.py: {e}")
        return False

def main():
    print("=" * 60)
    print("Tesseract OCR Configuration Helper")
    print("=" * 60)
    
    if platform.system() != 'Windows':
        print("This script is designed for Windows. On Linux/Mac, install via package manager.")
        return
    
    # Find Tesseract installations
    found_paths = find_tesseract_windows()
    
    if not found_paths:
        print("\n❌ No Tesseract installation found!")
        print("\nPlease install Tesseract OCR:")
        print("1. Download from: https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. Install to default location (C:\\Program Files\\Tesseract-OCR\\)")
        print("3. Run this script again")
        return
    
    print(f"\n✓ Found {len(found_paths)} Tesseract installation(s)")
    
    # Test each path
    working_paths = []
    print("\nTesting installations:")
    for path in found_paths:
        print(f"\nTesting: {path}")
        if test_tesseract_path(path):
            working_paths.append(path)
    
    if not working_paths:
        print("\n❌ No working Tesseract installations found!")
        return
    
    # Use the first working path
    best_path = working_paths[0]
    print(f"\n✓ Using Tesseract at: {best_path}")
    
    # Update the document processor
    if update_document_processor(best_path):
        print("\n🎉 Configuration complete!")
        print("\nNow run: python test_app.py")
    else:
        print(f"\n⚠️  Please manually update document_processor.py")
        print(f"Set: pytesseract.pytesseract.tesseract_cmd = r'{best_path}'")

if __name__ == "__main__":
    main()
