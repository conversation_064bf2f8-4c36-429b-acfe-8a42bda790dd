#!/usr/bin/env python3
"""
Debug test to see what text is being extracted
"""

import os
from PIL import Image, ImageDraw

def debug_pan_processing():
    """Debug PAN card processing"""
    print("Debugging PAN card processing...")
    
    try:
        from document_processor import DocumentProcessor
        from document_types import DocumentTypeManager
        
        # Create a simple test image with PAN card text
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # Add PAN card text
        pan_text = """INCOME TAX DEPARTMENT
GOVT. OF INDIA
PERMANENT ACCOUNT NUMBER
**********
Name: RAJESH KUMAR
Father's Name: MOHAN LAL
Date of Birth: 15/08/1985"""
        
        draw.text((20, 20), pan_text, fill='black')
        img.save('debug_pan.png')
        
        # Initialize processor
        processor = DocumentProcessor()
        doc_manager = DocumentTypeManager()
        
        # Extract text
        raw_text = processor.extract_text_from_image('debug_pan.png')
        print(f"Extracted text:\n{raw_text}")
        print(f"Text length: {len(raw_text)}")
        
        # Test document type identification
        doc_type, confidence = doc_manager.identify_document_type(raw_text)
        print(f"Document type: {doc_type}")
        print(f"Confidence: {confidence}")
        
        # Test individual patterns
        print("\nTesting individual patterns:")
        pan_config = doc_manager.get_type_config('PAN Card')
        print(f"PAN keywords: {pan_config.get('keywords', [])}")
        
        # Check keyword matches
        text_lower = raw_text.lower()
        for keyword in pan_config.get('keywords', []):
            if keyword in text_lower:
                print(f"✓ Found keyword: {keyword}")
            else:
                print(f"✗ Missing keyword: {keyword}")
        
        # Clean up
        if os.path.exists('debug_pan.png'):
            os.remove('debug_pan.png')
        
        return True
        
    except Exception as e:
        print(f"Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 60)
    print("Debug Document Processing")
    print("=" * 60)
    
    debug_pan_processing()

if __name__ == "__main__":
    main()
