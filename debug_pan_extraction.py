#!/usr/bin/env python3
"""
Debug PAN card data extraction with the actual extracted text
"""

import re

def debug_pan_extraction():
    """Debug PAN extraction with the actual text from ash-pan.jpg"""
    
    # This is the actual text extracted from ash-pan.jpg
    actual_text = """INCOME TAX DEPARTMENT Rs GOVT. OF INDIA
Seas
Sate Permanent Account Number Card
9, PS
RS **********
/ Name
ASHWAMEGH GAJANAN GANGASAGAR '
/ Father's Name - 8
GAJANAN GANGASAGAR ts 4
/ Date of Birth r """
    
    print("🔍 Debugging PAN Card Data Extraction")
    print("=" * 60)
    print("Actual extracted text:")
    print("-" * 40)
    print(repr(actual_text))
    print("-" * 40)
    
    # Test PAN Number extraction
    print("\n1. PAN Number Extraction:")
    pan_patterns = [
        r'([A-Z]{5}[0-9]{4}[A-Z])',  # Standard PAN format
        r'PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z])',  # With PAN prefix
    ]
    for i, pattern in enumerate(pan_patterns, 1):
        matches = re.findall(pattern, actual_text)
        print(f"   Pattern {i}: {pattern}")
        print(f"   Matches: {matches}")
    
    # Test Name extraction
    print("\n2. Name Extraction:")
    name_patterns = [
        r'(?:^|\n)\s*Name[:\s]*\n?\s*([A-Z][A-Z\s]+?)(?:\s*[/\n]|$)',  # Name on next line
        r'(?:^|\n)\s*Name[:\s]*([A-Z][A-Z\s]+?)(?:\s*[/\n]|Father)',   # Name on same line
        r'/\s*Name[:\s]*\n?\s*([A-Z][A-Z\s]+?)(?:\s*[/\n]|$)',        # With slash prefix
        r'/\s*Name\s*\n([A-Z][A-Z\s]+?)(?:\s*[/\n\'"]|$)',            # Specific for this text
    ]
    for i, pattern in enumerate(name_patterns, 1):
        matches = re.findall(pattern, actual_text, re.MULTILINE | re.IGNORECASE)
        print(f"   Pattern {i}: {pattern}")
        print(f"   Matches: {matches}")
    
    # Test Father's Name extraction
    print("\n3. Father's Name Extraction:")
    father_patterns = [
        r'Father[\'s]*\s*Name\s*[-\s]*\s*\d*\s*\n\s*([A-Z][A-Z\s]+?)(?:\s*[/\n]|$)',  # Name on next line with optional numbers
        r'(?:Father[\'s]*\s*Name)\s*[-\s]*\s*\d*[:\s]*([A-Z][A-Z\s]+?)(?:\s*[/\n]|$)',  # Name on same line
        r'/\s*Father[\'s]*\s*Name\s*[-\s]*\s*\d*\s*\n?\s*([A-Z][A-Z\s]+?)(?:\s*[/\n]|$)',  # With slash prefix
        r'/\s*Father[\'s]*\s*Name\s*[-\s]*\s*\d*\s*\n([A-Z][A-Z\s]+?)(?:\s*[/\n\w]|$)',  # Specific for this text
    ]
    for i, pattern in enumerate(father_patterns, 1):
        matches = re.findall(pattern, actual_text, re.MULTILINE | re.IGNORECASE)
        print(f"   Pattern {i}: {pattern}")
        print(f"   Matches: {matches}")
    
    # Test Date of Birth extraction
    print("\n4. Date of Birth Extraction:")
    dob_patterns = [
        r'(?:Date\s*of\s*Birth|DOB)[:\s]*r?\s*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',  # With label
        r'/\s*Date\s*of\s*Birth[:\s]*r?\s*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',     # With slash prefix
        r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})',                                      # Standalone date
        r'(\d{1,2}\.\d{1,2}\.\d{4})',                                          # Dot separated
    ]
    for i, pattern in enumerate(dob_patterns, 1):
        matches = re.findall(pattern, actual_text, re.MULTILINE | re.IGNORECASE)
        print(f"   Pattern {i}: {pattern}")
        print(f"   Matches: {matches}")
    
    # Line by line analysis
    print("\n5. Line by Line Analysis:")
    lines = actual_text.split('\n')
    for i, line in enumerate(lines):
        print(f"   Line {i}: '{line.strip()}'")
        if 'ASHWAMEGH' in line:
            print(f"      -> Contains name!")
        if 'GAJANAN' in line and 'ASHWAMEGH' not in line:
            print(f"      -> Contains father's name!")
    
    # Test improved patterns based on the actual text structure
    print("\n6. Improved Patterns for This Text:")
    
    # Name - the name appears after "/ Name" on the next line
    name_match = re.search(r'/\s*Name\s*\n([A-Z][A-Z\s]+?)(?:\s*[\'"]|$)', actual_text, re.MULTILINE)
    if name_match:
        name = name_match.group(1).strip()
        print(f"   ✅ Name found: '{name}'")
    else:
        print(f"   ❌ Name not found")
    
    # Father's name - appears after "/ Father's Name - 8" on the next line
    father_match = re.search(r'/\s*Father[\'s]*\s*Name\s*[-\s]*\s*\d*\s*\n([A-Z][A-Z\s]+?)(?:\s*[a-z]|$)', actual_text, re.MULTILINE)
    if father_match:
        father_name = father_match.group(1).strip()
        print(f"   ✅ Father's name found: '{father_name}'")
    else:
        print(f"   ❌ Father's name not found")

def main():
    debug_pan_extraction()

if __name__ == "__main__":
    main()
