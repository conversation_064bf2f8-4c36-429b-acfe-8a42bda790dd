#!/usr/bin/env python3
"""
Explain the OCR process and parameters used in the document extraction system
"""

def explain_ocr_parameters():
    """Explain the OCR parameters and methods used"""
    print("=" * 60)
    print("OCR PROCESS AND PARAMETERS EXPLANATION")
    print("=" * 60)
    
    print("\n1. TESSERACT OCR ENGINE:")
    print("   - Engine: Tesseract 4.x (Google's open-source OCR)")
    print("   - Language: English ('eng')")
    print("   - Additional languages available: Hindi ('hin'), Marathi ('mar')")
    
    print("\n2. OCR CONFIGURATION PARAMETERS:")
    print("   We use different PSM (Page Segmentation Mode) values:")
    print("   - PSM 6: Uniform block of text (default)")
    print("   - PSM 3: Fully automatic page segmentation")
    print("   - PSM 8: Single word recognition")
    
    print("\n3. PREPROCESSING METHODS:")
    print("   We try multiple preprocessing approaches:")
    
    print("\n   A) STANDARD PREPROCESSING:")
    print("      - Resize image if < 800x600 pixels")
    print("      - Convert to grayscale")
    print("      - CLAHE contrast enhancement (clipLimit=2.0)")
    print("      - Median blur noise reduction (kernel=3)")
    print("      - OTSU threshold for binary image")
    
    print("\n   B) GENTLE PREPROCESSING (for clear images):")
    print("      - Minimal processing")
    print("      - Light CLAHE enhancement (clipLimit=1.5)")
    print("      - No aggressive filtering")
    
    print("\n   C) AGGRESSIVE PREPROCESSING (for poor quality):")
    print("      - Significant upscaling (to 1000x800 minimum)")
    print("      - Strong CLAHE enhancement (clipLimit=3.0)")
    print("      - Multiple noise reduction steps")
    print("      - Bilateral filtering")
    print("      - Sharpening kernel")
    print("      - Adaptive threshold")
    print("      - Morphological operations")
    
    print("\n   D) PAN-SPECIFIC PREPROCESSING:")
    print("      - Optimized for PAN card color schemes")
    print("      - Enhanced contrast for blue/green backgrounds")
    print("      - Adaptive threshold tuned for PAN cards")
    
    print("\n4. OCR EXECUTION STRATEGY:")
    print("   - Try all preprocessing methods with different PSM modes")
    print("   - Select result with most extracted text")
    print("   - Fallback to original image if preprocessing fails")

def show_actual_ocr_code():
    """Show the actual OCR implementation"""
    print("\n" + "=" * 60)
    print("ACTUAL OCR IMPLEMENTATION")
    print("=" * 60)
    
    print("\nHere's how we actually call Tesseract:")
    print("""
# Method 1: Standard OCR call
text = pytesseract.image_to_string(
    image,                    # PIL Image object
    lang='eng',              # Language: English
    config='--psm 6'         # Page Segmentation Mode 6
)

# Method 2: With confidence data
data = pytesseract.image_to_data(
    image,
    lang='eng',
    output_type=pytesseract.Output.DICT
)
# This gives us confidence scores for each word

# Method 3: Different PSM modes we try
configs = [
    '--psm 6',  # Uniform block of text
    '--psm 3',  # Fully automatic page segmentation  
    '--psm 8'   # Single word
]
""")

def explain_data_extraction_process():
    """Explain how we extract structured data from OCR text"""
    print("\n" + "=" * 60)
    print("DATA EXTRACTION PROCESS")
    print("=" * 60)
    
    print("\n1. DOCUMENT TYPE IDENTIFICATION:")
    print("   - Keyword matching (e.g., 'income tax', 'permanent account')")
    print("   - Pattern matching (e.g., PAN number format: **********)")
    print("   - Confidence scoring based on matches found")
    print("   - Special bonus for strong patterns (like PAN numbers)")
    
    print("\n2. STRUCTURED DATA EXTRACTION:")
    print("   For PAN Cards, we extract:")
    print("   - PAN Number: Regex pattern [A-Z]{5}[0-9]{4}[A-Z]")
    print("   - Name: Standalone name line after PAN number")
    print("   - Father's Name: Text after 'Father's Name' label")
    print("   - Date of Birth: Date patterns DD/MM/YYYY")
    
    print("\n3. NAME EXTRACTION STRATEGY:")
    print("   - Look for standalone lines with 2-4 capitalized words")
    print("   - Must appear AFTER PAN number but BEFORE 'Father's' section")
    print("   - Validate against known non-name patterns")
    print("   - Length and format validation")
    
    print("\n4. PATTERN MATCHING EXAMPLES:")
    print("   - PAN Number: r'([A-Z]{5}[0-9]{4}[A-Z])'")
    print("   - Date: r'(\\d{1,2}[/-]\\d{1,2}[/-]\\d{4})'")
    print("   - Name: Line-by-line analysis with validation")
    print("   - Father's Name: r'Father[\\s]*Name\\s*\\n\\s*([A-Z][A-Z\\s]+?)'")

def demonstrate_ocr_process():
    """Demonstrate the actual OCR process"""
    print("\n" + "=" * 60)
    print("LIVE OCR DEMONSTRATION")
    print("=" * 60)
    
    try:
        from document_processor import DocumentProcessor
        from PIL import Image, ImageDraw
        import pytesseract
        
        # Create a sample PAN card image
        img = Image.new('RGB', (500, 300), color='lightblue')
        draw = ImageDraw.Draw(img)
        
        sample_text = """INCOME TAX DEPARTMENT
GOVT. OF INDIA
PERMANENT ACCOUNT NUMBER

**********

ASHWAMEGH GAJANAN GANGASAGAR

Father's Name
GAJANAN GANGASAGAR

Date of Birth
16/11/1998"""
        
        draw.text((20, 20), sample_text, fill='black')
        img.save('demo_pan.png')
        
        print("\n1. CREATED SAMPLE IMAGE: demo_pan.png")
        
        processor = DocumentProcessor()
        
        print("\n2. TRYING DIFFERENT PREPROCESSING METHODS:")
        
        methods = ['gentle', 'standard', 'aggressive', 'pan_specific']
        
        for method in methods:
            try:
                print(f"\n   Method: {method.upper()}")
                processed_img = processor.preprocess_image('demo_pan.png', method=method)
                
                # Convert to PIL and extract text
                pil_img = Image.fromarray(processed_img)
                
                # Try different PSM modes
                psm_modes = ['--psm 6', '--psm 3', '--psm 8']
                
                for psm in psm_modes:
                    try:
                        text = pytesseract.image_to_string(pil_img, lang='eng', config=psm)
                        char_count = len(text.strip())
                        print(f"     {psm}: {char_count} characters extracted")
                        
                        if char_count > 100:  # Show sample if good extraction
                            lines = text.strip().split('\n')[:3]
                            print(f"     Sample: {' | '.join(lines)}")
                        
                    except Exception as e:
                        print(f"     {psm}: Failed - {e}")
                        
            except Exception as e:
                print(f"   {method}: Failed - {e}")
        
        print("\n3. DOCUMENT TYPE IDENTIFICATION:")
        raw_text = processor.extract_text_from_image('demo_pan.png')
        doc_type = processor.identify_document_type(raw_text)
        print(f"   Detected type: {doc_type}")
        
        print("\n4. STRUCTURED DATA EXTRACTION:")
        if doc_type == 'PAN Card':
            structured_data = processor.extract_pan_data(raw_text)
            for key, value in structured_data.items():
                print(f"   {key}: {value}")
        
        # Clean up
        import os
        if os.path.exists('demo_pan.png'):
            os.remove('demo_pan.png')
            
    except Exception as e:
        print(f"Demonstration failed: {e}")
        import traceback
        traceback.print_exc()

def explain_confidence_scoring():
    """Explain how confidence scoring works"""
    print("\n" + "=" * 60)
    print("CONFIDENCE SCORING SYSTEM")
    print("=" * 60)
    
    print("\n1. KEYWORD SCORING:")
    print("   - Count matching keywords (e.g., 'income tax', 'permanent account')")
    print("   - Calculate: keyword_matches / total_keywords")
    print("   - Example: 5 matches out of 8 keywords = 0.625 score")
    
    print("\n2. PATTERN SCORING:")
    print("   - Count matching regex patterns (PAN number, dates, etc.)")
    print("   - Calculate: pattern_matches / total_patterns")
    print("   - Example: 3 matches out of 5 patterns = 0.600 score")
    
    print("\n3. FINAL CONFIDENCE CALCULATION:")
    print("   - For PAN cards with PAN number detected:")
    print("     confidence = (keyword_score * 0.4) + (pattern_score * 0.6) + 0.2 bonus")
    print("   - For other documents:")
    print("     confidence = (keyword_score * 0.5) + (pattern_score * 0.5)")
    
    print("\n4. THRESHOLD SYSTEM:")
    print("   - PAN Card threshold: 0.25 (lower due to PAN number bonus)")
    print("   - Other documents: 0.3-0.6 depending on type")
    print("   - Documents below threshold = 'Unknown'")

def main():
    explain_ocr_parameters()
    show_actual_ocr_code()
    explain_data_extraction_process()
    explain_confidence_scoring()
    demonstrate_ocr_process()

if __name__ == "__main__":
    main()
