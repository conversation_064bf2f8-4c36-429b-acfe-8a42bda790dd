#!/usr/bin/env python3
"""
Test the fixed data extraction
"""

def test_ash_pan_extraction():
    """Test extraction for ash-pan.jpg case"""
    print("Testing ash-pan.jpg data extraction...")
    
    sample_text = """INCOME TAX DEPARTMENT
GOVT. OF INDIA
PERMANENT ACCOUNT NUMBER

**********

ASHWAMEGH GAJANAN GANGASAGAR

Father's Name
GAJANAN GANGASAGAR

Date of Birth
16/11/1998

Signature"""
    
    try:
        from document_processor import DocumentProcessor
        
        processor = DocumentProcessor()
        extracted_data = processor.extract_pan_data(sample_text)
        
        print("Extracted data:")
        for key, value in extracted_data.items():
            print(f"  {key}: '{value}'")
        
        # Check expected results
        expected = {
            'PAN Number': '**********',
            'Name': 'ASHWAMEGH GAJANAN GANGASAGAR',
            'Father\'s Name': 'GAJANAN GANGASAGAR',
            'Date of Birth': '16/11/1998'
        }
        
        print(f"\nValidation:")
        passed = 0
        for field, expected_value in expected.items():
            actual_value = extracted_data.get(field, '')
            if actual_value == expected_value:
                print(f"✓ {field}: CORRECT")
                passed += 1
            else:
                print(f"✗ {field}: Expected '{expected_value}', got '{actual_value}'")
        
        print(f"\nResult: {passed}/{len(expected)} fields correct")
        return passed == len(expected)
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_pan_formats():
    """Test with different PAN card text formats"""
    print("\nTesting different PAN card formats...")
    
    test_cases = [
        {
            'name': 'Format 1: Name on separate line',
            'text': """INCOME TAX DEPARTMENT
PERMANENT ACCOUNT NUMBER
**********
RAJESH KUMAR SHARMA
Father's Name: MOHAN LAL SHARMA
DOB: 15/08/1985""",
            'expected_name': 'RAJESH KUMAR SHARMA'
        },
        {
            'name': 'Format 2: Name with label',
            'text': """INCOME TAX DEPARTMENT
PERMANENT ACCOUNT NUMBER
**********
Name: PRIYA SHARMA
Father's Name: RAMESH SHARMA
Date of Birth: 22/07/1990""",
            'expected_name': 'PRIYA SHARMA'
        },
        {
            'name': 'Format 3: Multi-line layout',
            'text': """INCOME TAX DEPARTMENT
GOVT. OF INDIA

PERMANENT ACCOUNT NUMBER

**********

AMIT KUMAR PATEL

Father's Name
KUMAR PATEL

Date of Birth
10/03/1988""",
            'expected_name': 'AMIT KUMAR PATEL'
        }
    ]
    
    try:
        from document_processor import DocumentProcessor
        
        processor = DocumentProcessor()
        passed = 0
        
        for case in test_cases:
            print(f"\n--- {case['name']} ---")
            extracted_data = processor.extract_pan_data(case['text'])
            
            actual_name = extracted_data.get('Name', '')
            expected_name = case['expected_name']
            
            print(f"Expected name: '{expected_name}'")
            print(f"Actual name: '{actual_name}'")
            
            if actual_name == expected_name:
                print("✓ Name extraction: CORRECT")
                passed += 1
            else:
                print("✗ Name extraction: INCORRECT")
        
        print(f"\nOverall: {passed}/{len(test_cases)} formats passed")
        return passed >= 2
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def test_full_document_processing():
    """Test the full document processing pipeline"""
    print("\nTesting full document processing...")
    
    sample_text = """INCOME TAX DEPARTMENT
GOVT. OF INDIA
PERMANENT ACCOUNT NUMBER

**********

ASHWAMEGH GAJANAN GANGASAGAR

Father's Name
GAJANAN GANGASAGAR

Date of Birth
16/11/1998

Signature"""
    
    try:
        from document_processor import DocumentProcessor
        from document_types import DocumentTypeManager
        
        processor = DocumentProcessor()
        doc_manager = DocumentTypeManager()
        
        # Test document type identification
        doc_type, confidence = doc_manager.identify_document_type(sample_text)
        print(f"Document type: {doc_type} (confidence: {confidence:.3f})")
        
        # Test structured data extraction
        if doc_type == 'PAN Card':
            structured_data = processor.extract_structured_data(sample_text, doc_type)
            print(f"Structured data extraction:")
            for key, value in structured_data.items():
                print(f"  {key}: '{value}'")
            
            # Check if we got the main fields
            required_fields = ['PAN Number', 'Name']
            found_fields = [field for field in required_fields if field in structured_data and structured_data[field]]
            
            if len(found_fields) == len(required_fields):
                print("✓ Full processing: SUCCESS")
                return True
            else:
                print(f"✗ Full processing: Missing fields {set(required_fields) - set(found_fields)}")
                return False
        else:
            print("✗ Document type not recognized")
            return False
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def main():
    print("=" * 60)
    print("Testing Fixed Data Extraction")
    print("=" * 60)
    
    tests = [
        test_ash_pan_extraction,
        test_different_pan_formats,
        test_full_document_processing
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ PASSED")
            else:
                print("✗ FAILED")
        except Exception as e:
            print(f"✗ ERROR: {e}")
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 Data extraction is now working perfectly!")
        print("\nFixed issues:")
        print("- Name extraction now gets the correct person name")
        print("- Father's name extraction is more precise")
        print("- Better handling of multi-line PAN card layouts")
        print("- Improved pattern matching for different formats")
    else:
        print("⚠️  Some extraction issues remain")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
