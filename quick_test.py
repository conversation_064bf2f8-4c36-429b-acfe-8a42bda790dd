#!/usr/bin/env python3
"""
Quick test to verify the application is working
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def create_test_image():
    """Create a test image with text"""
    # Create a white image
    img = Image.new('RGB', (400, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    # Add some text
    try:
        # Try to use a default font
        font = ImageFont.load_default()
    except:
        font = None
    
    text = "INCOME TAX DEPARTMENT\nPERMANENT ACCOUNT NUMBER\nABCDE1234F\nJOHN DOE"
    draw.text((20, 20), text, fill='black', font=font)
    
    # Save the test image
    img.save('test_document.png')
    print("✓ Created test image: test_document.png")
    return 'test_document.png'

def test_ocr():
    """Test OCR functionality"""
    print("Testing OCR functionality...")
    
    try:
        from document_processor import DocumentProcessor
        
        # Create test image
        test_image = create_test_image()
        
        # Initialize processor
        processor = DocumentProcessor()
        
        # Process the test document
        result = processor.process_document(test_image, 'test_document.png')
        
        print("✓ OCR processing successful!")
        print(f"Document Type: {result.get('Document Type', 'Unknown')}")
        print(f"Raw Text: {result.get('Raw Text', '')[:100]}...")
        
        # Clean up
        if os.path.exists(test_image):
            os.remove(test_image)
        
        return True
        
    except Exception as e:
        print(f"✗ OCR test failed: {e}")
        return False

def test_flask_import():
    """Test Flask app import"""
    print("Testing Flask app import...")
    
    try:
        from app import app
        print("✓ Flask app imported successfully")
        print(f"✓ App configured with upload folder: {app.config.get('UPLOAD_FOLDER')}")
        return True
    except Exception as e:
        print(f"✗ Flask app import failed: {e}")
        return False

def main():
    print("=" * 50)
    print("Quick Application Test")
    print("=" * 50)
    
    tests = [
        test_flask_import,
        test_ocr
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ Test failed: {e}")
            print()
    
    print("=" * 50)
    if passed == len(tests):
        print("🎉 All tests passed! Your application is working correctly!")
        print("\nTo start the web application:")
        print("1. Run: python app.py")
        print("2. Open browser to: http://localhost:5000")
        print("3. Upload your documents and see the magic!")
    else:
        print(f"⚠️  {passed}/{len(tests)} tests passed")
        print("Some functionality may not work correctly.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
