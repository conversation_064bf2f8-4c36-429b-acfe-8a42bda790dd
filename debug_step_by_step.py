#!/usr/bin/env python3
"""
Debug extraction step by step
"""

import re

def debug_father_extraction():
    """Debug father's name extraction step by step"""
    
    actual_text = """INCOME TAX DEPARTMENT Rs GOVT. OF INDIA
Seas
Sate Permanent Account Number Card
9, PS
RS **********
/ Name
ASHWAMEGH GAJANAN GANGASAGAR '
/ Father's Name - 8
GAJANAN GANGASAGAR ts 4
/ Date of Birth r """
    
    print("🔍 Step-by-step Father's Name Extraction")
    print("=" * 60)
    
    # Test the exact patterns from the code
    father_patterns = [
        r'/\s*Father[\'s]*\s*Name\s*[-\s]*\s*\d*\s*\n([A-Z][A-Z\s]+?)(?:\s*[a-z]|$)',  # Slash + Father's Name + newline (most common)
        r'Father[\'s]*\s*Name\s*[-\s]*\s*\d*\s*\n\s*([A-Z][A-Z\s]+?)(?:\s*[/\n]|$)',  # Name on next line with optional numbers
        r'(?:Father[\'s]*\s*Name)\s*[-\s]*\s*\d*[:\s]*([A-Z][A-Z\s]+?)(?:\s*[/\n]|$)',  # Name on same line
        r'/\s*Father[\'s]*\s*Name\s*[-\s]*\s*\d*\s*\n?\s*([A-Z][A-Z\s]+?)(?:\s*[/\n]|$)',  # With slash prefix
    ]
    
    for i, pattern in enumerate(father_patterns, 1):
        print(f"\nPattern {i}: {pattern}")
        father_match = re.search(pattern, actual_text, re.IGNORECASE | re.MULTILINE)
        if father_match:
            raw_name = father_match.group(1)
            print(f"  Raw match: '{raw_name}'")
            
            # Apply cleaning steps
            father_name = raw_name.strip()
            print(f"  After strip: '{father_name}'")
            
            father_name = re.sub(r'\s*[a-z].*$', '', father_name).strip()
            print(f"  After removing lowercase: '{father_name}'")
            
            father_name = re.sub(r'\s*\d+.*$', '', father_name).strip()
            print(f"  After removing numbers: '{father_name}'")
            
            father_name = re.sub(r'[^\w\s].*$', '', father_name).strip()
            print(f"  After removing special chars: '{father_name}'")
            
            father_name = re.sub(r'\s+', ' ', father_name)
            print(f"  Final cleaned: '{father_name}'")
            
            # Test validation
            from document_processor import DocumentProcessor
            processor = DocumentProcessor()
            is_valid = processor._is_valid_pan_name(father_name)
            print(f"  Is valid: {is_valid}")
            
            if father_name and is_valid:
                print(f"  ✅ SUCCESS: Would extract '{father_name}'")
                break
            else:
                print(f"  ❌ FAILED: Would not extract")
        else:
            print(f"  No match")

if __name__ == "__main__":
    debug_father_extraction()
