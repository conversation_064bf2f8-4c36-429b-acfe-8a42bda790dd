#!/usr/bin/env python3
"""
Comprehensive system diagnosis for document extraction issues
"""

import os
import sys
import platform
import subprocess
import importlib

def check_python_environment():
    """Check Python environment and required packages"""
    print("🐍 Python Environment Check")
    print("-" * 40)
    print(f"Python version: {sys.version}")
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Architecture: {platform.architecture()[0]}")
    
    # Check required packages
    required_packages = [
        'pytesseract',
        'PIL',
        'cv2',
        'numpy',
        'flask',
        'werkzeug'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
                print(f"✅ {package}: {PIL.__version__}")
            elif package == 'cv2':
                import cv2
                print(f"✅ {package}: {cv2.__version__}")
            else:
                module = importlib.import_module(package)
                version = getattr(module, '__version__', 'unknown')
                print(f"✅ {package}: {version}")
        except ImportError:
            print(f"❌ {package}: Not installed")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print(f"Install with: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_tesseract_installation():
    """Check Tesseract OCR installation"""
    print(f"\n🔍 Tesseract OCR Check")
    print("-" * 40)
    
    # Try to run tesseract --version
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.strip().split('\n')[0]
            print(f"✅ Tesseract found in PATH: {version_line}")
            
            # Check available languages
            lang_result = subprocess.run(['tesseract', '--list-langs'], 
                                       capture_output=True, text=True, timeout=10)
            if lang_result.returncode == 0:
                languages = lang_result.stdout.strip().split('\n')[1:]  # Skip header
                print(f"📚 Available languages: {', '.join(languages[:10])}")
                if len(languages) > 10:
                    print(f"    ... and {len(languages) - 10} more")
                
                # Check for required languages
                required_langs = ['eng', 'hin']
                missing_langs = [lang for lang in required_langs if lang not in languages]
                if missing_langs:
                    print(f"⚠️  Missing languages: {', '.join(missing_langs)}")
                else:
                    print(f"✅ Required languages available: {', '.join(required_langs)}")
            
            return True
        else:
            print(f"❌ Tesseract error: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print(f"❌ Tesseract not found in PATH")
        
        # Check common Windows installation paths
        if platform.system() == 'Windows':
            print(f"🔍 Checking common Windows installation paths...")
            common_paths = [
                r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
                r'C:\tesseract\tesseract.exe'
            ]
            
            found_paths = []
            for path in common_paths:
                if os.path.exists(path):
                    print(f"✅ Found: {path}")
                    found_paths.append(path)
                else:
                    print(f"❌ Not found: {path}")
            
            if found_paths:
                print(f"\n💡 Tesseract found but not in PATH. You may need to:")
                print(f"   1. Add Tesseract to your system PATH, or")
                print(f"   2. Update document_processor.py with the correct path")
                return False
            else:
                print(f"\n❌ Tesseract not found. Please install from:")
                print(f"   https://github.com/UB-Mannheim/tesseract/wiki")
                return False
        
        return False
    
    except subprocess.TimeoutExpired:
        print(f"❌ Tesseract command timed out")
        return False
    except Exception as e:
        print(f"❌ Error checking Tesseract: {e}")
        return False

def check_sample_images():
    """Check if sample images exist and are readable"""
    print(f"\n📁 Sample Images Check")
    print("-" * 40)
    
    uploads_dir = "uploads"
    if not os.path.exists(uploads_dir):
        print(f"❌ Uploads directory not found: {uploads_dir}")
        return False
    
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']
    image_files = []
    
    for file in os.listdir(uploads_dir):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            image_files.append(file)
    
    if not image_files:
        print(f"❌ No image files found in {uploads_dir}")
        return False
    
    print(f"✅ Found {len(image_files)} image files:")
    for img_file in sorted(image_files):
        file_path = os.path.join(uploads_dir, img_file)
        file_size = os.path.getsize(file_path)
        print(f"   📄 {img_file} ({file_size:,} bytes)")
    
    # Try to load one image with OpenCV
    try:
        import cv2
        test_image = os.path.join(uploads_dir, image_files[0])
        img = cv2.imread(test_image)
        if img is not None:
            height, width = img.shape[:2]
            print(f"✅ Successfully loaded test image: {width}x{height}")
            return True
        else:
            print(f"❌ Could not load test image with OpenCV")
            return False
    except Exception as e:
        print(f"❌ Error loading test image: {e}")
        return False

def test_basic_ocr():
    """Test basic OCR functionality"""
    print(f"\n🧪 Basic OCR Test")
    print("-" * 40)
    
    try:
        from document_processor import DocumentProcessor
        processor = DocumentProcessor()
        print(f"✅ DocumentProcessor imported successfully")
        
        # Test with a sample image if available
        uploads_dir = "uploads"
        if os.path.exists(uploads_dir):
            image_files = [f for f in os.listdir(uploads_dir) 
                          if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            if image_files:
                test_image = os.path.join(uploads_dir, image_files[0])
                print(f"🧪 Testing OCR with: {image_files[0]}")
                
                try:
                    text = processor.extract_text_from_image(test_image)
                    if text and len(text.strip()) > 0:
                        print(f"✅ OCR successful: {len(text)} characters extracted")
                        print(f"📝 Sample: {text[:100]}...")
                        return True
                    else:
                        print(f"⚠️  OCR returned empty text")
                        return False
                except Exception as e:
                    print(f"❌ OCR failed: {e}")
                    return False
        
        print(f"⚠️  No test images available for OCR test")
        return True
        
    except Exception as e:
        print(f"❌ Error importing DocumentProcessor: {e}")
        return False

def main():
    print("🔧 Document Extraction System Diagnosis")
    print("=" * 60)
    
    checks = [
        ("Python Environment", check_python_environment),
        ("Tesseract Installation", check_tesseract_installation),
        ("Sample Images", check_sample_images),
        ("Basic OCR", test_basic_ocr)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f"❌ {check_name} check failed with error: {e}")
            results[check_name] = False
    
    # Summary
    print(f"\n📊 Diagnosis Summary")
    print("=" * 60)
    
    all_passed = True
    for check_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {check_name}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 All checks passed! Your system should be working correctly.")
        print(f"💡 If you're still having issues, try running: python test_improved_ocr.py")
    else:
        print(f"\n⚠️  Some checks failed. Please address the issues above.")
        print(f"💡 Common solutions:")
        print(f"   - Install missing Python packages: pip install -r requirements.txt")
        print(f"   - Install Tesseract OCR from: https://github.com/UB-Mannheim/tesseract/wiki")
        print(f"   - Add sample images to the uploads/ directory")

if __name__ == "__main__":
    main()
