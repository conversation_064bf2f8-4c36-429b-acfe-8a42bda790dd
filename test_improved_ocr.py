#!/usr/bin/env python3
"""
Test the improved OCR system with adaptive PSM selection
"""

import os
import sys
from document_processor import DocumentProcessor
import cv2
import numpy as np

def test_image_analysis(image_path):
    """Test image analysis and PSM selection"""
    print(f"\n{'='*60}")
    print(f"Testing: {os.path.basename(image_path)}")
    print(f"{'='*60}")
    
    if not os.path.exists(image_path):
        print(f"❌ Image not found: {image_path}")
        return False
    
    try:
        # Initialize processor
        processor = DocumentProcessor()
        
        # Analyze image characteristics
        img = cv2.imread(image_path)
        if img is None:
            print(f"❌ Could not load image: {image_path}")
            return False
            
        height, width = img.shape[:2]
        aspect_ratio = width / height
        
        print(f"📐 Image dimensions: {width}x{height}")
        print(f"📐 Aspect ratio: {aspect_ratio:.2f}")
        
        # Determine orientation
        if aspect_ratio > 1.4:
            orientation = "Landscape (card-like)"
        elif aspect_ratio < 0.8:
            orientation = "Portrait (document-like)"
        else:
            orientation = "Square-ish"
        
        print(f"📐 Orientation: {orientation}")
        
        # Get optimal PSM modes
        optimal_psm_modes = processor.get_optimal_psm_modes(image_path)
        print(f"🎯 Optimal PSM modes: {optimal_psm_modes}")
        
        # Extract text using improved method
        print(f"\n🔍 Extracting text...")
        extracted_text = processor.extract_text_from_image(image_path)
        
        print(f"\n📝 Extracted text length: {len(extracted_text)} characters")
        print(f"📝 Text quality score: {processor._calculate_text_quality(extracted_text):.2f}")
        
        if extracted_text:
            print(f"\n📄 First 200 characters of extracted text:")
            print("-" * 40)
            print(repr(extracted_text[:200]))
            print("-" * 40)
            
            # Try to identify document type
            doc_type = processor.identify_document_type(extracted_text)
            print(f"\n🏷️  Identified document type: {doc_type}")
            
            # Extract structured data if document type is recognized
            if doc_type != 'Unknown':
                structured_data = processor.extract_structured_data(extracted_text, doc_type)
                print(f"\n📊 Extracted structured data:")
                for key, value in structured_data.items():
                    print(f"  {key}: {value}")
            else:
                print(f"\n❓ Document type unknown - getting suggestions...")
                suggestions = processor.get_document_suggestions(extracted_text)
                if suggestions['possible_types']:
                    print(f"💡 Possible types:")
                    for suggestion in suggestions['possible_types'][:3]:
                        print(f"  - {suggestion['type']} (confidence: {suggestion['confidence']})")
        else:
            print(f"❌ No text extracted!")
            
        return len(extracted_text) > 0
        
    except Exception as e:
        print(f"❌ Error testing {image_path}: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_sample_images():
    """Test all sample images in the uploads directory"""
    print(f"🧪 Testing Improved OCR System")
    print(f"{'='*80}")
    
    uploads_dir = "uploads"
    if not os.path.exists(uploads_dir):
        print(f"❌ Uploads directory not found: {uploads_dir}")
        return
    
    # Get all image files
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']
    image_files = []
    
    for file in os.listdir(uploads_dir):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            image_files.append(os.path.join(uploads_dir, file))
    
    if not image_files:
        print(f"❌ No image files found in {uploads_dir}")
        return
    
    print(f"📁 Found {len(image_files)} image files")
    
    successful_tests = 0
    total_tests = len(image_files)
    
    for image_path in sorted(image_files):
        success = test_image_analysis(image_path)
        if success:
            successful_tests += 1
    
    print(f"\n{'='*80}")
    print(f"🏁 Test Summary")
    print(f"{'='*80}")
    print(f"✅ Successful: {successful_tests}/{total_tests}")
    print(f"❌ Failed: {total_tests - successful_tests}/{total_tests}")
    
    if successful_tests == total_tests:
        print(f"🎉 All tests passed!")
    elif successful_tests > 0:
        print(f"⚠️  Some tests failed - check individual results above")
    else:
        print(f"💥 All tests failed - check Tesseract installation and configuration")

def test_specific_image(image_name):
    """Test a specific image"""
    image_path = os.path.join("uploads", image_name)
    return test_image_analysis(image_path)

def main():
    if len(sys.argv) > 1:
        # Test specific image
        image_name = sys.argv[1]
        test_specific_image(image_name)
    else:
        # Test all images
        test_all_sample_images()

if __name__ == "__main__":
    main()
