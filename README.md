# Document OCR Extraction System

A comprehensive document extraction application that uses OCR (Optical Character Recognition) to extract data from government ID documents like PAN cards, Aadhaar cards, driving licenses, passports, and voter IDs.

## Features

- **Multi-document Upload**: Upload multiple documents at once
- **Automatic OCR**: Extract text from images using Tesseract OCR
- **Document Type Recognition**: Automatically identify document types
- **Structured Data Extraction**: Extract specific fields (names, numbers, dates, etc.)
- **Learning System**: Improve recognition over time by learning from processed documents
- **JSON Output**: Generate structured JSON output grouped by person/entity
- **Web Interface**: User-friendly web interface with drag-and-drop functionality

## Supported Document Types

- PAN Card
- Aadhaar Card
- Driving License
- Passport
- Voter ID
- Unknown documents (with learning capability)

## Installation

### Prerequisites

1. **Python 3.8+**
2. **Tesseract OCR**

### Install Tesseract OCR

#### Windows:
1. Download Tesseract from: https://github.com/UB-Mannheim/tesseract/wiki
2. Install and note the installation path (usually `C:\Program Files\Tesseract-OCR\tesseract.exe`)
3. Add Tesseract to your system PATH or update the path in `document_processor.py`

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install tesseract-ocr
```

#### macOS:
```bash
brew install tesseract
```

### Install Python Dependencies

1. Clone or download this repository
2. Navigate to the project directory
3. Install required packages:

```bash
pip install -r requirements.txt
```

## Configuration

If Tesseract is not in your system PATH, update the path in `document_processor.py`:

```python
# Uncomment and update this line with your Tesseract path
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
```

## Usage

### Running the Application

1. Start the Flask application:
```bash
python app.py
```

2. Open your web browser and navigate to:
```
http://localhost:5000
```

3. Upload your documents using the web interface
4. View the extracted data and download the JSON results

### API Endpoints

- `GET /` - Main web interface
- `POST /upload` - Upload and process documents
- `GET /document-types` - Get list of supported document types
- `POST /add-document-type` - Add new document type
- `POST /learn-document` - Teach the system about document types

## Output Format

The system generates JSON output in the following format:

```json
[
    {
        "Person Name": {
            "documents": [
                {
                    "filename": "document.jpg",
                    "Document Type": "PAN Card",
                    "PAN Number": "**********",
                    "Name": "John Doe",
                    "Father's Name": "Jane Doe",
                    "Date of Birth": "01/01/1990"
                }
            ]
        }
    }
]
```

## File Structure

```
├── app.py                 # Main Flask application
├── document_processor.py  # OCR and document processing logic
├── document_types.py      # Document type management and learning
├── requirements.txt       # Python dependencies
├── templates/
│   └── index.html        # Web interface
├── uploads/              # Uploaded files (created automatically)
├── document_types.json   # Document type configurations (created automatically)
└── learning_data.json    # Learning data (created automatically)
```

## Adding New Document Types

The system can learn new document types in two ways:

1. **Automatic Learning**: When processing unknown documents, the system can suggest document types based on partial matches.

2. **Manual Addition**: Use the API to add new document types with custom patterns:

```python
# Example: Adding a new document type
doc_type_manager.add_new_type(
    type_name="New ID Card",
    keywords=["new", "identification", "card"],
    patterns={
        "ID Number": r"ID[:\s]*([A-Z0-9]+)",
        "Name": r"Name[:\s]*([A-Z\s]+)"
    },
    confidence_threshold=0.6
)
```

## Troubleshooting

### Common Issues

1. **Tesseract not found**: Make sure Tesseract is installed and the path is correctly set
2. **Poor OCR results**: Try preprocessing images (resize, enhance contrast) before upload
3. **Document not recognized**: Use the learning feature to teach the system about new document types

### Improving OCR Accuracy

- Use high-resolution images (300 DPI or higher)
- Ensure good lighting and contrast
- Avoid skewed or rotated images
- Remove noise and artifacts from images

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.
