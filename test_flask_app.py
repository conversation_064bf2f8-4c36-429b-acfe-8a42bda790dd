#!/usr/bin/env python3
"""
Test the Flask application functionality
"""

import os
import sys
import json
from io import BytesIO

def test_flask_imports():
    """Test if Flask and related imports work"""
    try:
        print("Testing Flask imports...")
        import flask
        print(f"✅ Flask version: {flask.__version__}")
        
        from werkzeug.utils import secure_filename
        print("✅ Werkzeug imported successfully")
        
        from document_processor import DocumentProcessor
        print("✅ DocumentProcessor imported successfully")
        
        from document_types import DocumentTypeManager
        print("✅ DocumentTypeManager imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_document_processing():
    """Test document processing functionality"""
    try:
        print("\nTesting document processing...")
        
        # Initialize processor
        processor = DocumentProcessor()
        print("✅ DocumentProcessor initialized")
        
        # Test with a sample image
        test_image = "uploads/ash-pan.jpg"
        if os.path.exists(test_image):
            print(f"📄 Testing with: {test_image}")
            
            # Extract text
            text = processor.extract_text_from_image(test_image)
            print(f"✅ Text extracted: {len(text)} characters")
            
            # Identify document type
            doc_type = processor.identify_document_type(text)
            print(f"✅ Document type: {doc_type}")
            
            # Extract structured data
            if doc_type != 'Unknown':
                structured_data = processor.extract_structured_data(text, doc_type)
                print(f"✅ Structured data extracted: {len(structured_data)} fields")
                for key, value in structured_data.items():
                    print(f"   {key}: {value}")
            
            return True
        else:
            print(f"⚠️  Test image not found: {test_image}")
            return False
            
    except Exception as e:
        print(f"❌ Processing error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_flask_app_creation():
    """Test Flask app creation"""
    try:
        print("\nTesting Flask app creation...")
        
        from flask import Flask
        test_app = Flask(__name__)
        print("✅ Flask app created successfully")
        
        # Test a simple route
        @test_app.route('/test')
        def test_route():
            return "Test successful"
        
        print("✅ Test route added")
        
        # Test app configuration
        test_app.config['TESTING'] = True
        print("✅ App configuration set")
        
        return True
        
    except Exception as e:
        print(f"❌ Flask app creation error: {e}")
        return False

def test_main_app_import():
    """Test importing the main app"""
    try:
        print("\nTesting main app import...")
        
        # Add current directory to path
        sys.path.insert(0, os.getcwd())
        
        # Try to import the main app
        from app import app
        print("✅ Main app imported successfully")
        
        # Test app configuration
        print(f"✅ Upload folder: {app.config.get('UPLOAD_FOLDER', 'Not set')}")
        print(f"✅ Max content length: {app.config.get('MAX_CONTENT_LENGTH', 'Not set')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Main app import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🧪 Testing Flask Application")
    print("=" * 60)
    
    tests = [
        ("Flask Imports", test_flask_imports),
        ("Document Processing", test_document_processing),
        ("Flask App Creation", test_flask_app_creation),
        ("Main App Import", test_main_app_import),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n📊 Test Summary")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print(f"\n🎉 All tests passed! Flask app should work correctly.")
        print(f"💡 Try running: python app.py")
    else:
        print(f"\n⚠️  Some tests failed. Please address the issues above.")

if __name__ == "__main__":
    main()
