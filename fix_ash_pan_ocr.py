#!/usr/bin/env python3
"""
Fix OCR issues specifically for ash-pan.jpg
"""

def test_different_ocr_approaches():
    """Test different OCR approaches for ash-pan.jpg"""
    print("Testing different OCR approaches for ash-pan.jpg...")
    
    try:
        import pytesseract
        from PIL import Image
        import cv2
        import numpy as np
        
        # Test if ash-pan.jpg exists
        try:
            img = Image.open('ash-pan.jpg')
            print(f"✓ Found ash-pan.jpg - Size: {img.size}")
        except:
            print("✗ ash-pan.jpg not found in current directory")
            return False
        
        # Test different OCR configurations
        configs = [
            ('English only, PSM 6', 'eng', '--psm 6'),
            ('English only, PSM 3', 'eng', '--psm 3'),
            ('English only, PSM 8', 'eng', '--psm 8'),
            ('English only, PSM 11', 'eng', '--psm 11'),
            ('English only, PSM 13', 'eng', '--psm 13'),
            ('English + Hindi, PSM 6', 'eng+hin', '--psm 6'),
            ('English + Hindi, PSM 3', 'eng+hin', '--psm 3'),
        ]
        
        print("\nTesting different OCR configurations:")
        print("-" * 60)
        
        best_result = ""
        best_config = ""
        best_score = 0
        
        for name, lang, psm in configs:
            try:
                text = pytesseract.image_to_string(img, lang=lang, config=psm)
                
                # Score based on finding key elements
                score = 0
                if 'ASHWAMEGH' in text or 'GAJANAN' in text:
                    score += 10
                if '**********' in text:
                    score += 5
                if 'INCOME TAX' in text or 'income tax' in text.lower():
                    score += 3
                if '16/11/1998' in text:
                    score += 2
                
                char_count = len(text.strip())
                
                print(f"{name:25} | {char_count:3d} chars | Score: {score:2d}")
                
                if score > best_score:
                    best_score = score
                    best_result = text
                    best_config = name
                
                # Show sample if it looks good
                if score >= 5:
                    lines = [line.strip() for line in text.split('\n') if line.strip()][:5]
                    print(f"  Sample: {' | '.join(lines)}")
                
            except Exception as e:
                print(f"{name:25} | ERROR: {e}")
        
        print(f"\nBest configuration: {best_config} (Score: {best_score})")
        
        if best_score > 0:
            print(f"\nBest OCR result:")
            print("-" * 40)
            print(best_result)
            print("-" * 40)
        
        return best_score > 5
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def test_enhanced_preprocessing():
    """Test enhanced preprocessing specifically for ash-pan.jpg"""
    print("\nTesting enhanced preprocessing for ash-pan.jpg...")
    
    try:
        import cv2
        import numpy as np
        from PIL import Image
        import pytesseract
        
        # Load image
        img = cv2.imread('ash-pan.jpg')
        if img is None:
            print("✗ Could not load ash-pan.jpg")
            return False
        
        print(f"Original image shape: {img.shape}")
        
        # Try different preprocessing approaches
        preprocessing_methods = [
            ('Original', lambda x: x),
            ('Grayscale + CLAHE', preprocess_grayscale_clahe),
            ('High contrast', preprocess_high_contrast),
            ('Denoised', preprocess_denoise),
            ('Sharpened', preprocess_sharpen),
            ('Blue channel only', preprocess_blue_channel),
        ]
        
        best_result = ""
        best_method = ""
        best_score = 0
        
        for method_name, preprocess_func in preprocessing_methods:
            try:
                processed_img = preprocess_func(img.copy())
                
                # Convert to PIL
                if len(processed_img.shape) == 3:
                    pil_img = Image.fromarray(cv2.cvtColor(processed_img, cv2.COLOR_BGR2RGB))
                else:
                    pil_img = Image.fromarray(processed_img)
                
                # OCR with best config from previous test
                text = pytesseract.image_to_string(pil_img, lang='eng+hin', config='--psm 6')
                
                # Score the result
                score = 0
                if 'ASHWAMEGH' in text:
                    score += 10
                if 'GAJANAN' in text:
                    score += 5
                if '**********' in text:
                    score += 5
                if 'INCOME TAX' in text or 'income tax' in text.lower():
                    score += 3
                
                char_count = len(text.strip())
                print(f"{method_name:20} | {char_count:3d} chars | Score: {score:2d}")
                
                if score > best_score:
                    best_score = score
                    best_result = text
                    best_method = method_name
                
                # Save processed image for inspection
                cv2.imwrite(f'processed_{method_name.lower().replace(" ", "_")}.png', processed_img)
                
            except Exception as e:
                print(f"{method_name:20} | ERROR: {e}")
        
        print(f"\nBest preprocessing: {best_method} (Score: {best_score})")
        
        if best_score > 0:
            print(f"\nBest preprocessed OCR result:")
            print("-" * 40)
            print(best_result)
            print("-" * 40)
        
        return best_score > 5
        
    except Exception as e:
        print(f"Enhanced preprocessing test failed: {e}")
        return False

def preprocess_grayscale_clahe(img):
    """Grayscale with CLAHE enhancement"""
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)
    return enhanced

def preprocess_high_contrast(img):
    """High contrast preprocessing"""
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    # Increase contrast
    enhanced = cv2.convertScaleAbs(gray, alpha=1.5, beta=0)
    # Apply threshold
    _, thresh = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    return thresh

def preprocess_denoise(img):
    """Denoising preprocessing"""
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    # Denoise
    denoised = cv2.fastNlMeansDenoising(gray)
    # Enhance contrast
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    enhanced = clahe.apply(denoised)
    return enhanced

def preprocess_sharpen(img):
    """Sharpening preprocessing"""
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    # Sharpening kernel
    kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
    sharpened = cv2.filter2D(gray, -1, kernel)
    return sharpened

def preprocess_blue_channel(img):
    """Extract blue channel (PAN cards often have blue backgrounds)"""
    # Split channels
    b, g, r = cv2.split(img)
    # Use blue channel as it often has better contrast for PAN cards
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    enhanced = clahe.apply(b)
    return enhanced

def main():
    print("=" * 60)
    print("FIXING ASH-PAN.JPG OCR ISSUES")
    print("=" * 60)
    
    # Test 1: Different OCR configurations
    config_success = test_different_ocr_approaches()
    
    # Test 2: Enhanced preprocessing
    preprocess_success = test_enhanced_preprocessing()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    if config_success or preprocess_success:
        print("✓ Found working OCR approach for ash-pan.jpg!")
        print("\nRecommendations:")
        print("1. Use the best configuration identified above")
        print("2. Check the processed images saved in current directory")
        print("3. Update the system to use the optimal preprocessing")
    else:
        print("⚠️  Need to try additional approaches")
        print("\nNext steps:")
        print("1. Try manual image enhancement tools")
        print("2. Experiment with different image formats")
        print("3. Consider using multiple OCR engines")

if __name__ == "__main__":
    main()
