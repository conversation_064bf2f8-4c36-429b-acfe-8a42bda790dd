#!/usr/bin/env python3
"""
Test script for multi-language OCR functionality
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont
import numpy as np

def create_test_documents():
    """Create test images with different document types"""
    
    # Test PAN Card (English + Hindi)
    pan_img = Image.new('RGB', (500, 300), color='white')
    draw = ImageDraw.Draw(pan_img)
    
    pan_text = """INCOME TAX DEPARTMENT
आयकर विभाग
GOVT. OF INDIA
भारत सरकार

PERMANENT ACCOUNT NUMBER
स्थायी खाता संख्या

**********

Name: RAJESH KUMAR SHARMA
नाम: राजेश कुमार शर्मा

Father's Name: MOHAN LAL SHARMA
पिता का नाम: मोहन लाल शर्मा

Date of Birth: 15/08/1985
जन्म तिथि: 15/08/1985"""
    
    draw.text((20, 20), pan_text, fill='black')
    pan_img.save('test_pan_multilang.png')
    print("✓ Created test PAN card with Hindi text")
    
    # Test Aadhaar Card (English + Hindi)
    aadhaar_img = Image.new('RGB', (500, 300), color='white')
    draw = ImageDraw.Draw(aadhaar_img)
    
    aadhaar_text = """भारत सरकार
GOVERNMENT OF INDIA

आधार
AADHAAR

1234 5678 9012

Name: प्रिया शर्मा
PRIYA SHARMA

DOB: 22/07/1990
जन्म तिथि: 22/07/1990

Gender: Female / महिला

Address: गांव पोस्ट तहसील जिला
Village Post Tehsil District"""
    
    draw.text((20, 20), aadhaar_text, fill='black')
    aadhaar_img.save('test_aadhaar_multilang.png')
    print("✓ Created test Aadhaar card with Hindi text")
    
    # Test Driving License (English + Hindi)
    dl_img = Image.new('RGB', (500, 300), color='white')
    draw = ImageDraw.Draw(dl_img)
    
    dl_text = """DRIVING LICENCE
चालक अनुज्ञप्ति

DL No: MH1420110012345
परवाना संख्या: MH1420110012345

Name: अमित पटेल
AMIT PATEL

Father's Name: रमेश पटेल
S/O: RAMESH PATEL

DOB: 10/03/1988
जन्म तिथि: 10/03/1988

Valid Till: 09/03/2028
वैध तक: 09/03/2028"""
    
    draw.text((20, 20), dl_text, fill='black')
    dl_img.save('test_dl_multilang.png')
    print("✓ Created test Driving License with Hindi text")
    
    return ['test_pan_multilang.png', 'test_aadhaar_multilang.png', 'test_dl_multilang.png']

def test_document_processing():
    """Test document processing with multi-language support"""
    print("\nTesting multi-language document processing...")
    
    try:
        from document_processor import DocumentProcessor
        
        # Create test documents
        test_files = create_test_documents()
        
        # Initialize processor
        processor = DocumentProcessor()
        
        results = []
        
        for test_file in test_files:
            print(f"\nProcessing: {test_file}")
            
            # Process the document
            result = processor.process_document(test_file, test_file)
            
            print(f"Document Type: {result.get('Document Type', 'Unknown')}")
            print(f"Extracted Fields: {list(result.keys())}")
            
            # Show some extracted text
            raw_text = result.get('Raw Text', '')
            if raw_text:
                print(f"Raw Text (first 100 chars): {raw_text[:100]}...")
            
            results.append(result)
            
            # Clean up test file
            if os.path.exists(test_file):
                os.remove(test_file)
        
        return results
        
    except Exception as e:
        print(f"✗ Multi-language test failed: {e}")
        return []

def test_language_detection():
    """Test if Hindi and Marathi languages are available"""
    print("Testing language availability...")
    
    try:
        import pytesseract
        
        # Get available languages
        langs = pytesseract.get_languages()
        print(f"Available languages: {langs}")
        
        required_langs = ['eng', 'hin', 'mar']
        missing_langs = [lang for lang in required_langs if lang not in langs]
        
        if missing_langs:
            print(f"⚠️  Missing languages: {missing_langs}")
            print("Run: python install_languages.py to install missing languages")
            return False
        else:
            print("✓ All required languages available")
            return True
            
    except Exception as e:
        print(f"✗ Language detection failed: {e}")
        return False

def test_ocr_with_languages():
    """Test OCR with different language configurations"""
    print("\nTesting OCR with different language configurations...")
    
    try:
        import pytesseract
        from PIL import Image, ImageDraw
        
        # Create a simple test image with Hindi text
        img = Image.new('RGB', (300, 100), color='white')
        draw = ImageDraw.Draw(img)
        
        # Add Hindi text
        hindi_text = "नाम: राजेश कुमार\nपिता: मोहन लाल"
        draw.text((20, 20), hindi_text, fill='black')
        
        img.save('test_hindi.png')
        
        # Test different language configurations
        configs = [
            ('eng', 'English only'),
            ('hin', 'Hindi only'),
            ('eng+hin', 'English + Hindi'),
            ('eng+hin+mar', 'English + Hindi + Marathi')
        ]
        
        for lang_config, description in configs:
            try:
                text = pytesseract.image_to_string(img, lang=lang_config)
                print(f"✓ {description}: {len(text.strip())} characters extracted")
            except Exception as e:
                print(f"✗ {description}: {e}")
        
        # Clean up
        if os.path.exists('test_hindi.png'):
            os.remove('test_hindi.png')
        
        return True
        
    except Exception as e:
        print(f"✗ OCR language test failed: {e}")
        return False

def main():
    print("=" * 60)
    print("Multi-Language OCR Test Suite")
    print("=" * 60)
    
    tests = [
        test_language_detection,
        test_ocr_with_languages,
        test_document_processing
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ Test failed: {e}")
            print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 Multi-language OCR is working correctly!")
        print("\nYour application now supports:")
        print("- English text recognition")
        print("- Hindi text recognition (हिंदी)")
        print("- Marathi text recognition (मराठी)")
        print("- Mixed language documents")
        print("- Enhanced document type detection")
    else:
        print("⚠️  Some tests failed.")
        print("\nTo fix issues:")
        print("1. Run: python install_languages.py")
        print("2. Ensure Tesseract is properly installed")
        print("3. Check that language packs are in tessdata folder")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
