#!/usr/bin/env python3
"""
Debug name validation
"""

from document_processor import DocumentProcessor

def test_name_validation():
    """Test name validation function"""
    
    processor = DocumentProcessor()
    
    test_names = [
        "ASHWAMEGH GAJANAN GANGASAGAR",
        "GAJANAN GANGASAGAR",
        "GAJANAN GANGASAGAR ts 4",
        "GAJANAN GANGASAGAR ts",
        "JOHN DOE",
        "INCOME TAX",
        "GOVT OF INDIA"
    ]
    
    print("🧪 Testing Name Validation")
    print("=" * 50)
    
    for name in test_names:
        is_valid = processor._is_valid_pan_name(name)
        print(f"'{name}' -> {'✅ Valid' if is_valid else '❌ Invalid'}")
        
        # Test cleaned version
        import re
        cleaned = re.sub(r'\s*[a-z].*$', '', name).strip()
        cleaned = re.sub(r'\s*\d+.*$', '', cleaned).strip()
        if cleaned != name:
            is_valid_cleaned = processor._is_valid_pan_name(cleaned)
            print(f"  Cleaned: '{cleaned}' -> {'✅ Valid' if is_valid_cleaned else '❌ Invalid'}")

if __name__ == "__main__":
    test_name_validation()
