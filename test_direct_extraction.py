#!/usr/bin/env python3
"""
Test the extraction method directly
"""

from document_processor import DocumentProcessor

def test_direct_extraction():
    """Test extraction with the actual text"""
    
    # Actual text from ash-pan.jpg
    actual_text = """INCOME TAX DEPARTMENT Rs GOVT. OF INDIA
Seas
Sate Permanent Account Number Card
9, PS
RS **********
/ Name
ASHWAMEGH GAJANAN GANGASAGAR '
/ Father's Name - 8
GAJANAN GANGASAGAR ts 4
/ Date of Birth r """
    
    print("🧪 Testing Direct Extraction")
    print("=" * 50)
    
    processor = DocumentProcessor()
    
    # Test PAN extraction
    print("Testing extract_pan_data method:")
    pan_data = processor.extract_pan_data(actual_text)
    
    print("Results:")
    for key, value in pan_data.items():
        print(f"  {key}: '{value}'")
    
    if not pan_data:
        print("  No data extracted!")
    
    # Test document type manager extraction
    print("\nTesting document type manager extraction:")
    structured_data = processor.extract_structured_data(actual_text, 'PAN Card')
    
    print("Results:")
    for key, value in structured_data.items():
        print(f"  {key}: '{value}'")
    
    if not structured_data:
        print("  No data extracted!")

if __name__ == "__main__":
    test_direct_extraction()
