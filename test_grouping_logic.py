#!/usr/bin/env python3
"""
Test the enhanced grouping logic for same person with different name formats
"""

import sys
import os

# Add the current directory to the path so we can import from app.py
sys.path.insert(0, os.getcwd())

from app import group_documents_by_person, normalize_name_for_grouping

def test_name_normalization():
    """Test name normalization function"""
    
    print("🧪 Testing Name Normalization")
    print("=" * 50)
    
    test_cases = [
        ("ASHWAMEGH GAJANAN GANGASAGAR", "Ashwamegh Gajanan Gangasagar"),
        ("Ashwamegh Gajanan Gangasagar", "Ashwamegh Gajanan Gangasagar"),
        ("ashwamegh gajanan gangasagar", "Ashwamegh Gajanan Gangasagar"),
        ("ASHWAMEGH  GAJANAN   GANGASAGAR", "Ashwamegh Gajanan Gangasagar"),
        ("Ashwamegh'Gajanan Gangasagar", "Ashwamegh Gajanan Gangasagar"),
        ("Unknown", "Unknown"),
    ]
    
    for input_name, expected in test_cases:
        result = normalize_name_for_grouping(input_name)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{input_name}' -> '{result}' (expected: '{expected}')")

def test_document_grouping():
    """Test document grouping with same person, different name formats"""
    
    print(f"\n🧪 Testing Document Grouping")
    print("=" * 50)
    
    # Sample documents for the same person with different name formats
    test_documents = [
        {
            "Document Type": "Aadhaar Card",
            "Name": "Ashwamegh Gajanan Gangasagar",
            "Aadhaar Number": "903 3238 9547",
            "Date of Birth": "16/11/1998",
            "Gender": "Male",
            "filename": "ash-adhar.jpg"
        },
        {
            "Document Type": "PAN Card", 
            "Name": "ASHWAMEGH GAJANAN GANGASAGAR",
            "PAN Number": "**********",
            "Father's Name": "GAJANAN GANGASAGAR",
            "Date of Birth": "16/11/1998",
            "filename": "ash-pan.jpg"
        }
    ]
    
    print("Input documents:")
    for i, doc in enumerate(test_documents, 1):
        print(f"  {i}. {doc['Document Type']}: '{doc['Name']}'")
    
    # Test grouping
    grouped_result = group_documents_by_person(test_documents)
    
    print(f"\nGrouping result:")
    print(f"Number of groups: {len(grouped_result)}")
    
    for group in grouped_result:
        for person_name, data in group.items():
            print(f"\n👤 Person: '{person_name}'")
            print(f"   Documents: {len(data['documents'])}")
            for doc in data['documents']:
                print(f"   - {doc['Document Type']}: {doc['filename']}")
    
    # Check if grouping worked correctly
    if len(grouped_result) == 1:
        print(f"\n✅ SUCCESS: Documents correctly grouped under one person")
        
        # Check the display name
        person_name = list(grouped_result[0].keys())[0]
        if person_name == "Ashwamegh Gajanan Gangasagar":
            print(f"✅ SUCCESS: Display name is in preferred format (title case)")
        else:
            print(f"⚠️  Display name: '{person_name}' (might prefer title case)")
            
    else:
        print(f"❌ FAILED: Documents split into {len(grouped_result)} groups instead of 1")

def test_mixed_case_scenarios():
    """Test various mixed case scenarios"""
    
    print(f"\n🧪 Testing Mixed Case Scenarios")
    print("=" * 50)
    
    test_scenarios = [
        {
            "name": "All caps vs Title case",
            "documents": [
                {"Document Type": "PAN Card", "Name": "JOHN DOE SMITH"},
                {"Document Type": "Aadhaar Card", "Name": "John Doe Smith"}
            ]
        },
        {
            "name": "With apostrophes",
            "documents": [
                {"Document Type": "PAN Card", "Name": "O'CONNOR JAMES"},
                {"Document Type": "Aadhaar Card", "Name": "Oconnor James"}
            ]
        },
        {
            "name": "Extra spaces",
            "documents": [
                {"Document Type": "PAN Card", "Name": "MARY   JANE   WATSON"},
                {"Document Type": "Aadhaar Card", "Name": "Mary Jane Watson"}
            ]
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n📋 Scenario: {scenario['name']}")
        grouped = group_documents_by_person(scenario['documents'])
        
        if len(grouped) == 1:
            person_name = list(grouped[0].keys())[0]
            print(f"   ✅ Grouped correctly under: '{person_name}'")
        else:
            print(f"   ❌ Split into {len(grouped)} groups")
            for group in grouped:
                for name in group.keys():
                    print(f"      - '{name}'")

def main():
    test_name_normalization()
    test_document_grouping()
    test_mixed_case_scenarios()

if __name__ == "__main__":
    main()
