#!/usr/bin/env python3
"""
Script to help install Tesseract language packs for Hindi and Marathi
"""

import os
import subprocess
import urllib.request
import platform

def download_language_pack(lang_code, tesseract_data_path):
    """Download language pack from GitHub"""
    base_url = "https://github.com/tesseract-ocr/tessdata/raw/main/"
    lang_file = f"{lang_code}.traineddata"
    url = base_url + lang_file
    
    target_path = os.path.join(tesseract_data_path, lang_file)
    
    if os.path.exists(target_path):
        print(f"✓ {lang_code} language pack already exists")
        return True
    
    try:
        print(f"Downloading {lang_code} language pack...")
        urllib.request.urlretrieve(url, target_path)
        print(f"✓ Downloaded {lang_code} language pack")
        return True
    except Exception as e:
        print(f"✗ Failed to download {lang_code}: {e}")
        return False

def find_tesseract_data_path():
    """Find Tesseract data directory"""
    possible_paths = []
    
    if platform.system() == 'Windows':
        possible_paths = [
            r'C:\Program Files\Tesseract-OCR\tessdata',
            r'C:\Program Files (x86)\Tesseract-OCR\tessdata',
            r'C:\tesseract\tessdata'
        ]
    else:
        possible_paths = [
            '/usr/share/tesseract-ocr/4.00/tessdata',
            '/usr/share/tesseract-ocr/tessdata',
            '/usr/local/share/tessdata',
            '/opt/homebrew/share/tessdata'
        ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return None

def check_language_support():
    """Check which languages are currently supported"""
    try:
        result = subprocess.run(['tesseract', '--list-langs'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            languages = result.stdout.strip().split('\n')[1:]  # Skip first line
            return languages
        else:
            # Try with full path
            tesseract_path = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
            if os.path.exists(tesseract_path):
                result = subprocess.run([tesseract_path, '--list-langs'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    languages = result.stdout.strip().split('\n')[1:]
                    return languages
    except Exception as e:
        print(f"Error checking languages: {e}")
    
    return []

def main():
    print("=" * 60)
    print("Tesseract Language Pack Installer")
    print("=" * 60)
    
    # Check current language support
    print("Checking current language support...")
    current_langs = check_language_support()
    
    if current_langs:
        print("Currently supported languages:")
        for lang in current_langs:
            print(f"  - {lang}")
    else:
        print("Could not detect current language support")
    
    # Check if Hindi and Marathi are already supported
    required_langs = ['hin', 'mar']
    missing_langs = [lang for lang in required_langs if lang not in current_langs]
    
    if not missing_langs:
        print("\n✓ All required languages (Hindi, Marathi) are already installed!")
        return
    
    print(f"\nMissing languages: {missing_langs}")
    
    # Find Tesseract data directory
    data_path = find_tesseract_data_path()
    
    if not data_path:
        print("\n❌ Could not find Tesseract data directory!")
        print("\nManual installation instructions:")
        print("1. Find your Tesseract installation directory")
        print("2. Navigate to the 'tessdata' folder")
        print("3. Download language files from:")
        print("   https://github.com/tesseract-ocr/tessdata")
        print("4. Download hin.traineddata and mar.traineddata")
        print("5. Place them in the tessdata folder")
        return
    
    print(f"\nFound Tesseract data directory: {data_path}")
    
    # Download missing language packs
    success_count = 0
    for lang in missing_langs:
        lang_name = {'hin': 'Hindi', 'mar': 'Marathi'}.get(lang, lang)
        print(f"\nInstalling {lang_name} ({lang})...")
        
        if download_language_pack(lang, data_path):
            success_count += 1
        else:
            print(f"Failed to install {lang_name}")
    
    print("\n" + "=" * 60)
    if success_count == len(missing_langs):
        print("🎉 All language packs installed successfully!")
        print("\nYou can now use Hindi and Marathi OCR!")
        print("Restart your application to use the new languages.")
    else:
        print(f"⚠️  {success_count}/{len(missing_langs)} language packs installed")
        print("\nSome installations failed. You may need to:")
        print("1. Run this script as administrator")
        print("2. Check your internet connection")
        print("3. Manually download the language files")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
