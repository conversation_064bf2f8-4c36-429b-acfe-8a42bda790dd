#!/usr/bin/env python3
"""
Test enhanced name extraction for ash-adhar.jpg
"""

import re
from document_processor import DocumentProcessor

def test_name_extraction():
    """Test name extraction with the actual corrupted text"""
    
    # Actual raw text from ash-adhar.jpg
    raw_text = """a Soh GOVERNMENTOR INDIA P="
OS eA Ok Ashwamegh'Gajanan ee
De fo i, Gangasagar ae a 7 _
a oe ee wT fa DOB: 16/11/1998... |
SS ORT MALE. ree gaemem |
W903 3238 9547 ,"""
    
    print("🔍 Testing Enhanced Name Extraction")
    print("=" * 60)
    print("Raw text:")
    print("-" * 30)
    print(repr(raw_text))
    print("-" * 30)
    
    processor = DocumentProcessor()
    
    # Test the new corrupted text extraction method
    print("\n🧪 Testing _extract_names_from_corrupted_text method:")
    name_candidates = processor._extract_names_from_corrupted_text(raw_text)
    print(f"Found {len(name_candidates)} candidates:")
    for i, candidate in enumerate(name_candidates, 1):
        print(f"  {i}. '{candidate}'")
    
    # Test full Aadhaar extraction
    print(f"\n🧪 Testing full extract_aadhaar_data method:")
    aadhaar_data = processor.extract_aadhaar_data(raw_text)
    
    print(f"Extracted data:")
    for key, value in aadhaar_data.items():
        print(f"  {key}: {value}")
    
    # Check if name was extracted
    if 'Name' in aadhaar_data:
        extracted_name = aadhaar_data['Name']
        expected_name = "Ashwamegh Gajanan Gangasagar"
        print(f"\n✅ Name extracted: '{extracted_name}'")
        print(f"🎯 Expected: '{expected_name}'")
        
        if extracted_name.lower() == expected_name.lower():
            print(f"🎉 PERFECT MATCH!")
        elif expected_name.lower() in extracted_name.lower() or extracted_name.lower() in expected_name.lower():
            print(f"✅ PARTIAL MATCH - Good enough!")
        else:
            print(f"❌ NO MATCH - Need improvement")
    else:
        print(f"\n❌ No name extracted")
    
    # Manual pattern testing
    print(f"\n🔬 Manual Pattern Testing:")
    
    # Test specific patterns for this text
    test_patterns = [
        r'([A-Z][a-z]+)\'([A-Z][a-z]+)\s+[a-z]+\s*\n[^A-Z]*([A-Z][a-z]+)',
        r'Ashwamegh\'([A-Z][a-z]+)',
        r'([A-Z][a-z]+)\'([A-Z][a-z]+)',
        r'Gangasagar',
    ]
    
    for i, pattern in enumerate(test_patterns, 1):
        matches = re.findall(pattern, raw_text, re.MULTILINE)
        print(f"  Pattern {i}: {pattern}")
        print(f"    Matches: {matches}")
    
    # Test line-by-line analysis
    print(f"\n📝 Line-by-line Analysis:")
    lines = raw_text.split('\n')
    for i, line in enumerate(lines):
        print(f"  Line {i}: '{line.strip()}'")
        
        # Look for name parts in each line
        name_parts = re.findall(r'([A-Z][a-z]+(?:\'[A-Z][a-z]+)?)', line)
        if name_parts:
            print(f"    Name parts found: {name_parts}")

def test_with_actual_image():
    """Test with the actual image file"""
    
    print(f"\n🖼️  Testing with actual ash-adhar.jpg image:")
    print("=" * 60)
    
    processor = DocumentProcessor()
    
    image_path = "uploads/ash-adhar.jpg"
    
    try:
        # Extract text
        text = processor.extract_text_from_image(image_path)
        print(f"📄 Extracted text length: {len(text)} characters")
        
        # Extract Aadhaar data
        aadhaar_data = processor.extract_aadhaar_data(text)
        
        print(f"\n📊 Extracted Aadhaar data:")
        for key, value in aadhaar_data.items():
            if key == 'Name':
                print(f"  ✨ {key}: '{value}' ✨")
            else:
                print(f"  {key}: {value}")
        
        # Check if we got the name
        if 'Name' in aadhaar_data:
            print(f"\n🎉 SUCCESS: Name extracted from actual image!")
        else:
            print(f"\n⚠️  Name not extracted from actual image")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    test_name_extraction()
    test_with_actual_image()

if __name__ == "__main__":
    main()
