#!/usr/bin/env python3
"""
Debug father's name extraction specifically
"""

import re

def test_father_patterns():
    """Test father's name patterns"""
    
    actual_text = """INCOME TAX DEPARTMENT Rs GOVT. OF INDIA
Seas
Sate Permanent Account Number Card
9, PS
RS **********
/ Name
ASHWAMEGH GAJANAN GANGASAGAR '
/ Father's Name - 8
GAJANAN GANGASAGAR ts 4
/ Date of Birth r """
    
    print("🔍 Testing Father's Name Patterns")
    print("=" * 50)
    print("Text around father's name:")
    lines = actual_text.split('\n')
    for i, line in enumerate(lines):
        if 'Father' in line or 'GAJANAN' in line:
            print(f"Line {i}: '{line}'")
    
    print("\nTesting patterns:")
    
    # Test the working pattern from debug
    working_pattern = r'/\s*Father[\'s]*\s*Name\s*[-\s]*\s*\d*\s*\n([A-Z][A-Z\s]+?)(?:\s*[a-z]|$)'
    match = re.search(working_pattern, actual_text, re.MULTILINE)
    if match:
        print(f"✅ Working pattern found: '{match.group(1)}'")
    else:
        print(f"❌ Working pattern failed")
    
    # Test simpler patterns
    simple_patterns = [
        r'/\s*Father[\'s]*\s*Name[^A-Z]*\n([A-Z][A-Z\s]+)',
        r'Father[\'s]*\s*Name[^A-Z]*\n([A-Z][A-Z\s]+)',
        r'/\s*Father[\'s]*\s*Name.*?\n([A-Z][A-Z\s]+)',
    ]
    
    for i, pattern in enumerate(simple_patterns, 1):
        matches = re.findall(pattern, actual_text, re.MULTILINE)
        print(f"Pattern {i}: {pattern}")
        print(f"Matches: {matches}")
        if matches:
            # Clean the match
            cleaned = matches[0].strip()
            cleaned = re.sub(r'\s*[a-z].*$', '', cleaned).strip()
            cleaned = re.sub(r'\s*\d+.*$', '', cleaned).strip()
            print(f"Cleaned: '{cleaned}'")

if __name__ == "__main__":
    test_father_patterns()
