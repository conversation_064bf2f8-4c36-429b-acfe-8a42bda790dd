#!/usr/bin/env python3
"""
Fix Tesseract configuration issues
"""

import os
import pytesseract
from PIL import Image

def test_tesseract_config():
    """Test and fix Tesseract configuration"""
    print("🔧 Testing Tesseract Configuration")
    print("=" * 50)
    
    # Set the Tesseract path explicitly
    tesseract_path = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
    
    if os.path.exists(tesseract_path):
        pytesseract.pytesseract.tesseract_cmd = tesseract_path
        print(f"✅ Set Tesseract path to: {tesseract_path}")
    else:
        print(f"❌ Tesseract not found at: {tesseract_path}")
        return False
    
    # Test with a simple image
    try:
        # Create a simple test image with text
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a white image with black text
        img = Image.new('RGB', (400, 100), color='white')
        draw = ImageDraw.Draw(img)
        
        # Use default font
        try:
            # Try to use a system font
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            # Fallback to default font
            font = ImageFont.load_default()
        
        draw.text((10, 30), "TEST 123 ABC", fill='black', font=font)
        
        # Save test image
        test_image_path = "test_ocr.png"
        img.save(test_image_path)
        print(f"📄 Created test image: {test_image_path}")
        
        # Test OCR
        text = pytesseract.image_to_string(img)
        print(f"🔍 OCR Result: '{text.strip()}'")
        
        # Clean up
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
        
        if "TEST" in text or "123" in text:
            print(f"✅ Tesseract is working correctly!")
            return True
        else:
            print(f"⚠️  Tesseract returned unexpected result")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Tesseract: {e}")
        return False

def test_real_image():
    """Test with a real document image"""
    print(f"\n🧪 Testing with Real Image")
    print("=" * 50)
    
    # Set Tesseract path
    tesseract_path = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
    pytesseract.pytesseract.tesseract_cmd = tesseract_path
    
    # Find a test image
    uploads_dir = "uploads"
    test_images = ["PAN.jpg", "ash-pan.jpg", "aadhar_1.jpg"]
    
    for img_name in test_images:
        img_path = os.path.join(uploads_dir, img_name)
        if os.path.exists(img_path):
            print(f"📄 Testing with: {img_name}")
            
            try:
                # Load image
                img = Image.open(img_path)
                print(f"📐 Image size: {img.size}")
                
                # Test different PSM modes
                psm_modes = [3, 6, 4, 8]
                
                for psm in psm_modes:
                    try:
                        config = f'--psm {psm}'
                        text = pytesseract.image_to_string(img, config=config)
                        text_length = len(text.strip())
                        print(f"  PSM {psm}: {text_length} characters")
                        
                        if text_length > 50:
                            print(f"    Sample: {text.strip()[:100]}...")
                            
                    except Exception as e:
                        print(f"  PSM {psm}: Error - {e}")
                
                return True
                
            except Exception as e:
                print(f"❌ Error with {img_name}: {e}")
                continue
    
    print(f"❌ No suitable test images found")
    return False

def main():
    print("🔧 Tesseract Configuration Fix")
    print("=" * 60)
    
    # Test basic configuration
    if test_tesseract_config():
        print(f"\n✅ Basic Tesseract test passed")
        
        # Test with real images
        if test_real_image():
            print(f"\n🎉 All tests passed! Tesseract is working correctly.")
            print(f"💡 You can now run: python test_improved_ocr.py")
        else:
            print(f"\n⚠️  Real image test failed")
    else:
        print(f"\n❌ Basic Tesseract test failed")
        print(f"💡 Please check Tesseract installation")

if __name__ == "__main__":
    main()
