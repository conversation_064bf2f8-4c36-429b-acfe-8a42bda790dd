#!/usr/bin/env python3
"""
Test script for different Aadhaar card formats
"""

def test_aadhaar_format_1():
    """Test Aadhaar format like the one you mentioned"""
    print("Testing Aadhaar Format 1 (like your example)...")
    
    # Simulate the text that might be extracted from your example
    sample_text = """Government of India
Unique Identification Authority of India
आधार
AADHAAR

Prakash Gopichand Rathod

**************

DOB: 01/07/1974
Gender: Male

Address: S/O: Gopichand Rathod, Ward No-14, Ward No-3, Ramnagar,
Deccan College Road, Near Balvikas Kendra, Yerwada, Pune 411006
City, Yerwada, Pune, Maharashtra, 411006"""
    
    try:
        from document_processor import DocumentProcessor
        
        processor = DocumentProcessor()
        
        # Test the improved Aadhaar extraction
        extracted_data = processor.extract_aadhaar_data(sample_text)
        
        print("Extracted Data:")
        for key, value in extracted_data.items():
            print(f"  {key}: {value}")
        
        # Check if name is correctly extracted
        expected_name = "Prakash Gopichand Rathod"
        actual_name = extracted_data.get('Name', '')
        
        if expected_name == actual_name:
            print("✓ Name extraction: CORRECT")
            return True
        else:
            print(f"✗ Name extraction: Expected '{expected_name}', got '{actual_name}'")
            return False
            
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def test_aadhaar_format_2():
    """Test another common Aadhaar format"""
    print("\nTesting Aadhaar Format 2 (different layout)...")
    
    sample_text = """भारत सरकार
GOVERNMENT OF INDIA
आधार
AADHAAR

Priya Sharma

DOB: 22/07/1990
Gender: Female

1234 5678 9012

Address: Village Post Tehsil District
State PIN Code"""
    
    try:
        from document_processor import DocumentProcessor
        
        processor = DocumentProcessor()
        extracted_data = processor.extract_aadhaar_data(sample_text)
        
        print("Extracted Data:")
        for key, value in extracted_data.items():
            print(f"  {key}: {value}")
        
        # Check key fields
        checks = [
            ('Name', 'Priya Sharma'),
            ('Aadhaar Number', '1234 5678 9012'),
            ('Date of Birth', '22/07/1990'),
            ('Gender', 'Female')
        ]
        
        passed = 0
        for field, expected in checks:
            actual = extracted_data.get(field, '')
            if expected == actual:
                print(f"✓ {field}: CORRECT")
                passed += 1
            else:
                print(f"✗ {field}: Expected '{expected}', got '{actual}'")
        
        return passed == len(checks)
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def test_aadhaar_format_3():
    """Test Aadhaar with Hindi text"""
    print("\nTesting Aadhaar Format 3 (with Hindi)...")
    
    sample_text = """भारत सरकार
GOVERNMENT OF INDIA
आधार / AADHAAR

Rajesh Kumar Sharma

आधार संख्या: **************
DOB: 15/08/1985
लिंग: पुरुष / Male

पता: गांव पोस्ट तहसील जिला
Address: Village Post Tehsil District"""
    
    try:
        from document_processor import DocumentProcessor
        
        processor = DocumentProcessor()
        extracted_data = processor.extract_aadhaar_data(sample_text)
        
        print("Extracted Data:")
        for key, value in extracted_data.items():
            print(f"  {key}: {value}")
        
        # For this test, we mainly want to ensure we get a clean name
        name = extracted_data.get('Name', '')
        if 'Rajesh Kumar Sharma' in name:
            print("✓ Name extraction from bilingual text: CORRECT")
            return True
        else:
            print(f"✗ Name extraction: Got '{name}'")
            return False
            
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def test_pan_extraction():
    """Test PAN card extraction to ensure it still works"""
    print("\nTesting PAN Card extraction...")
    
    sample_text = """INCOME TAX DEPARTMENT
GOVT. OF INDIA
PERMANENT ACCOUNT NUMBER

**********

Name: RAJESH KUMAR SHARMA
Father's Name: MOHAN LAL SHARMA
Date of Birth: 15/08/1985"""
    
    try:
        from document_processor import DocumentProcessor
        
        processor = DocumentProcessor()
        extracted_data = processor.extract_pan_data(sample_text)
        
        print("Extracted Data:")
        for key, value in extracted_data.items():
            print(f"  {key}: {value}")
        
        # Check key fields
        checks = [
            ('PAN Number', '**********'),
            ('Name', 'RAJESH KUMAR SHARMA'),
            ('Father\'s Name', 'MOHAN LAL SHARMA'),
            ('Date of Birth', '15/08/1985')
        ]
        
        passed = 0
        for field, expected in checks:
            actual = extracted_data.get(field, '')
            if expected == actual:
                print(f"✓ {field}: CORRECT")
                passed += 1
            else:
                print(f"✗ {field}: Expected '{expected}', got '{actual}'")
        
        return passed >= 3  # Allow some flexibility
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def main():
    print("=" * 60)
    print("Testing Improved Document Extraction")
    print("=" * 60)
    
    tests = [
        test_aadhaar_format_1,
        test_aadhaar_format_2,
        test_aadhaar_format_3,
        test_pan_extraction
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ PASSED")
            else:
                print("✗ FAILED")
        except Exception as e:
            print(f"✗ ERROR: {e}")
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{len(tests)} tests passed")
    
    if passed >= 3:
        print("🎉 Document extraction improvements are working!")
        print("\nKey improvements:")
        print("- Better name extraction for Aadhaar cards")
        print("- Support for multiple Aadhaar formats")
        print("- Improved pattern matching")
        print("- Better handling of bilingual text")
    else:
        print("⚠️  Some improvements need more work")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
