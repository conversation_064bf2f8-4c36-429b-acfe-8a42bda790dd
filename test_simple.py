#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test for enhanced document processing
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont

def test_enhanced_processing():
    """Test the enhanced document processing"""
    print("Testing enhanced document processing...")
    
    try:
        from document_processor import DocumentProcessor
        
        # Create a simple test image with PAN card text
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # Add PAN card text
        pan_text = """INCOME TAX DEPARTMENT
GOVT. OF INDIA
PERMANENT ACCOUNT NUMBER
**********
Name: RAJES<PERSON> KUMAR
Father's Name: MOHAN LAL
Date of Birth: 15/08/1985"""
        
        draw.text((20, 20), pan_text, fill='black')
        img.save('test_pan_simple.png')
        
        # Initialize processor
        processor = DocumentProcessor()
        
        # Process the document
        result = processor.process_document('test_pan_simple.png', 'test_pan_simple.png')
        
        print(f"Document Type: {result.get('Document Type', 'Unknown')}")
        print(f"Extracted Fields:")
        for key, value in result.items():
            if key != 'Raw Text':
                print(f"  {key}: {value}")
        
        # Clean up
        if os.path.exists('test_pan_simple.png'):
            os.remove('test_pan_simple.png')
        
        return result.get('Document Type') == 'PAN Card'
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def test_aadhaar_processing():
    """Test Aadhaar card processing"""
    print("\nTesting Aadhaar card processing...")
    
    try:
        from document_processor import DocumentProcessor
        
        # Create a simple test image with Aadhaar text
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # Add Aadhaar text
        aadhaar_text = """GOVERNMENT OF INDIA
AADHAAR
1234 5678 9012
Name: PRIYA SHARMA
DOB: 22/07/1990
Gender: Female
Address: Village Post Tehsil District"""
        
        draw.text((20, 20), aadhaar_text, fill='black')
        img.save('test_aadhaar_simple.png')
        
        # Initialize processor
        processor = DocumentProcessor()
        
        # Process the document
        result = processor.process_document('test_aadhaar_simple.png', 'test_aadhaar_simple.png')
        
        print(f"Document Type: {result.get('Document Type', 'Unknown')}")
        print(f"Extracted Fields:")
        for key, value in result.items():
            if key != 'Raw Text':
                print(f"  {key}: {value}")
        
        # Clean up
        if os.path.exists('test_aadhaar_simple.png'):
            os.remove('test_aadhaar_simple.png')
        
        return result.get('Document Type') == 'Aadhaar Card'
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def test_driving_license_processing():
    """Test Driving License processing"""
    print("\nTesting Driving License processing...")
    
    try:
        from document_processor import DocumentProcessor
        
        # Create a simple test image with DL text
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # Add DL text
        dl_text = """DRIVING LICENCE
DL No: MH1420110012345
Name: AMIT PATEL
Father's Name: RAMESH PATEL
DOB: 10/03/1988
Valid Till: 09/03/2028"""
        
        draw.text((20, 20), dl_text, fill='black')
        img.save('test_dl_simple.png')
        
        # Initialize processor
        processor = DocumentProcessor()
        
        # Process the document
        result = processor.process_document('test_dl_simple.png', 'test_dl_simple.png')
        
        print(f"Document Type: {result.get('Document Type', 'Unknown')}")
        print(f"Extracted Fields:")
        for key, value in result.items():
            if key != 'Raw Text':
                print(f"  {key}: {value}")
        
        # Clean up
        if os.path.exists('test_dl_simple.png'):
            os.remove('test_dl_simple.png')
        
        return result.get('Document Type') == 'Driving License'
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def main():
    print("=" * 60)
    print("Enhanced Document Processing Test")
    print("=" * 60)
    
    tests = [
        test_enhanced_processing,
        test_aadhaar_processing,
        test_driving_license_processing
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ PASSED")
            else:
                print("✗ FAILED")
        except Exception as e:
            print(f"✗ ERROR: {e}")
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{len(tests)} tests passed")
    
    if passed >= 2:
        print("🎉 Enhanced document processing is working!")
        print("\nImprovements made:")
        print("- Better image preprocessing")
        print("- Multi-language OCR support")
        print("- Enhanced document type detection")
        print("- Improved pattern matching")
        print("- Support for Hindi/Marathi keywords")
    else:
        print("⚠️  Some document types may not be detected correctly")
        print("Try uploading real documents to test further")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
