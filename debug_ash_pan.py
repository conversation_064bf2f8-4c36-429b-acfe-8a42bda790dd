#!/usr/bin/env python3
"""
Debug script for ash-pan.jpg recognition issue
"""

def debug_pan_recognition():
    """Debug why ash-pan.jpg is not being recognized"""
    print("Debugging ash-pan.jpg recognition...")
    
    # Simulate the extracted text (based on your description)
    sample_text = """INCOME TAX DEPARTMENT
GOVT. OF INDIA
PERMANENT ACCOUNT NUMBER

**********

ASHWAMEGH GAJANAN GANGASAGAR

Father's Name
GAJANAN GANGASAGAR

Date of Birth
16/11/1998

Signature"""
    
    try:
        from document_processor import DocumentProcessor
        from document_types import DocumentTypeManager
        
        processor = DocumentProcessor()
        doc_manager = DocumentTypeManager()
        
        print("Extracted text simulation:")
        print("-" * 40)
        print(sample_text)
        print("-" * 40)
        
        # Test document type identification
        doc_type, confidence = doc_manager.identify_document_type(sample_text)
        print(f"\nDocument type detected: {doc_type}")
        print(f"Confidence: {confidence:.3f}")
        
        # Test individual keyword matching
        print("\nKeyword analysis:")
        text_lower = sample_text.lower()
        pan_config = doc_manager.get_type_config('PAN Card')
        
        print(f"PAN keywords to find: {pan_config.get('keywords', [])}")
        found_keywords = []
        for keyword in pan_config.get('keywords', []):
            if keyword in text_lower:
                found_keywords.append(keyword)
                print(f"✓ Found: '{keyword}'")
            else:
                print(f"✗ Missing: '{keyword}'")
        
        print(f"\nKeywords found: {len(found_keywords)}/{len(pan_config.get('keywords', []))}")
        
        # Test pattern matching
        print("\nPattern analysis:")
        import re
        patterns = pan_config.get('patterns', {})
        found_patterns = []
        
        for field, pattern in patterns.items():
            matches = re.findall(pattern, sample_text, re.IGNORECASE)
            if matches:
                found_patterns.append(field)
                print(f"✓ {field}: {matches}")
            else:
                print(f"✗ {field}: No match")
        
        print(f"\nPatterns found: {len(found_patterns)}/{len(patterns)}")
        
        # Test data extraction
        print("\nData extraction test:")
        if doc_type == 'PAN Card':
            extracted_data = processor.extract_pan_data(sample_text)
        else:
            # Force extraction as PAN card
            extracted_data = processor.extract_pan_data(sample_text)
        
        print("Extracted fields:")
        for key, value in extracted_data.items():
            print(f"  {key}: {value}")
        
        # Calculate what the confidence should be
        keyword_score = len(found_keywords) / len(pan_config.get('keywords', [])) if pan_config.get('keywords') else 0
        pattern_score = len(found_patterns) / len(patterns) if patterns else 0
        
        print(f"\nConfidence calculation:")
        print(f"Keyword score: {keyword_score:.3f} ({len(found_keywords)}/{len(pan_config.get('keywords', []))})")
        print(f"Pattern score: {pattern_score:.3f} ({len(found_patterns)}/{len(patterns)})")
        
        # Check if we have minimum requirements
        if len(found_keywords) >= 2 or len(found_patterns) >= 1:
            expected_score = (keyword_score * 0.5) + (pattern_score * 0.5)
            print(f"Expected confidence: {expected_score:.3f}")
            
            threshold = 0.3  # Current threshold
            print(f"Threshold: {threshold}")
            
            if expected_score >= threshold:
                print("✓ Should be recognized as PAN Card")
            else:
                print("✗ Below threshold - not recognized")
        else:
            print("✗ Insufficient keywords/patterns")
        
        return True
        
    except Exception as e:
        print(f"Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_improved_patterns():
    """Test with improved patterns for better recognition"""
    print("\n" + "="*50)
    print("Testing improved PAN recognition patterns...")
    
    # Test various PAN card text formats
    test_cases = [
        "INCOME TAX DEPARTMENT PERMANENT ACCOUNT ********** ASHWAMEGH GAJANAN",
        "TAX DEPARTMENT GOVT INDIA PAN **********",
        "PERMANENT ACCOUNT NUMBER ********** INCOME TAX",
        "GOVT OF INDIA INCOME TAX DEPARTMENT **********"
    ]
    
    try:
        from document_types import DocumentTypeManager
        
        manager = DocumentTypeManager()
        
        for i, text in enumerate(test_cases, 1):
            print(f"\nTest case {i}: {text[:50]}...")
            doc_type, confidence = manager.identify_document_type(text)
            print(f"Result: {doc_type} (confidence: {confidence:.3f})")
            
            if doc_type == 'PAN Card':
                print("✓ Correctly identified")
            else:
                print("✗ Not identified")
        
        return True
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def main():
    print("=" * 60)
    print("Debugging ash-pan.jpg Recognition Issue")
    print("=" * 60)
    
    debug_pan_recognition()
    test_improved_patterns()

if __name__ == "__main__":
    main()
