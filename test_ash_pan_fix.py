#!/usr/bin/env python3
"""
Test script for ash-pan.jpg fix
"""

def test_improved_pan_recognition():
    """Test improved PAN recognition with the fixes"""
    print("Testing improved PAN recognition...")
    
    # Test cases that simulate different OCR extraction qualities
    test_cases = [
        # Case 1: Perfect extraction (like our debug simulation)
        {
            'name': '<PERSON> OCR',
            'text': """INCOME TAX DEPARTMENT
GOVT. OF INDIA
PERMANENT ACCOUNT NUMBER

**********

ASHWAMEGH GAJANAN GANGASAGAR

Father's Name
GAJANAN GANGASAGAR

Date of Birth
16/11/1998

Signature"""
        },
        # Case 2: Partial extraction (missing some keywords)
        {
            'name': 'Partial OCR',
            'text': """TAX DEPARTMENT
PERMANENT ACCOUNT

**********

ASHWAMEGH GAJANAN GANGASAGAR

Father GAJANAN GANGASAGAR

16/11/1998"""
        },
        # Case 3: Minimal extraction (just the essentials)
        {
            'name': 'Mini<PERSON> OCR',
            'text': """INCOME TAX
**********
ASHWAMEGH GAJANAN GANGASAGAR
GAJANAN GANGASAGAR"""
        },
        # Case 4: Poor OCR with noise
        {
            'name': 'Noisy OCR',
            'text': """INCQME TAX DEPARTMENT
GOVT QF INDIA
PERMANENT ACCQUNT NUMBER

**********

ASHWAMEGH GAJANAN GANGASAGAR

Father's Name
GAJANAN GANGASAGAR"""
        }
    ]
    
    try:
        from document_types import DocumentTypeManager
        
        manager = DocumentTypeManager()
        passed = 0
        
        for case in test_cases:
            print(f"\n--- {case['name']} ---")
            doc_type, confidence = manager.identify_document_type(case['text'])
            
            print(f"Text length: {len(case['text'])} characters")
            print(f"Result: {doc_type} (confidence: {confidence:.3f})")
            
            if doc_type == 'PAN Card':
                print("✓ Correctly identified as PAN Card")
                passed += 1
            else:
                print("✗ Not identified as PAN Card")
        
        print(f"\nOverall result: {passed}/{len(test_cases)} test cases passed")
        return passed >= 3  # At least 3 out of 4 should pass
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def test_pan_data_extraction():
    """Test PAN data extraction with the name from ash-pan.jpg"""
    print("\nTesting PAN data extraction...")
    
    sample_text = """INCOME TAX DEPARTMENT
GOVT. OF INDIA
PERMANENT ACCOUNT NUMBER

**********

ASHWAMEGH GAJANAN GANGASAGAR

Father's Name
GAJANAN GANGASAGAR

Date of Birth
16/11/1998"""
    
    try:
        from document_processor import DocumentProcessor
        
        processor = DocumentProcessor()
        extracted_data = processor.extract_pan_data(sample_text)
        
        print("Extracted data:")
        for key, value in extracted_data.items():
            print(f"  {key}: {value}")
        
        # Check specific fields
        expected_checks = [
            ('PAN Number', '**********'),
            ('Name', 'ASHWAMEGH GAJANAN GANGASAGAR'),
            ('Father\'s Name', 'GAJANAN GANGASAGAR'),
            ('Date of Birth', '16/11/1998')
        ]
        
        passed = 0
        for field, expected in expected_checks:
            actual = extracted_data.get(field, '')
            if expected == actual:
                print(f"✓ {field}: Correct")
                passed += 1
            else:
                print(f"✗ {field}: Expected '{expected}', got '{actual}'")
        
        return passed >= 3
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def test_confidence_improvements():
    """Test the confidence calculation improvements"""
    print("\nTesting confidence improvements...")
    
    # Test case with PAN number but few keywords
    test_text = "********** ASHWAMEGH GAJANAN GANGASAGAR TAX"
    
    try:
        from document_types import DocumentTypeManager
        
        manager = DocumentTypeManager()
        doc_type, confidence = manager.identify_document_type(test_text)
        
        print(f"Test text: {test_text}")
        print(f"Result: {doc_type} (confidence: {confidence:.3f})")
        
        # With the PAN number present and the bonus, this should be recognized
        if doc_type == 'PAN Card' and confidence > 0.3:
            print("✓ PAN number bonus working correctly")
            return True
        else:
            print("✗ PAN number bonus not working")
            return False
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def test_preprocessing_methods():
    """Test different preprocessing methods"""
    print("\nTesting preprocessing methods...")
    
    try:
        from document_processor import DocumentProcessor
        from PIL import Image, ImageDraw
        
        # Create a test PAN card image
        img = Image.new('RGB', (500, 300), color='lightblue')  # PAN cards often have blue background
        draw = ImageDraw.Draw(img)
        
        text = """INCOME TAX DEPARTMENT
GOVT. OF INDIA
PERMANENT ACCOUNT NUMBER
**********
ASHWAMEGH GAJANAN GANGASAGAR
Father's Name: GAJANAN GANGASAGAR
Date of Birth: 16/11/1998"""
        
        draw.text((20, 20), text, fill='black')
        img.save('test_pan_preprocessing.png')
        
        processor = DocumentProcessor()
        
        # Test different preprocessing methods
        methods = ['standard', 'gentle', 'aggressive', 'pan_specific']
        results = {}
        
        for method in methods:
            try:
                processed_img = processor.preprocess_image('test_pan_preprocessing.png', method=method)
                # Convert to PIL and extract text
                from PIL import Image
                import pytesseract
                pil_img = Image.fromarray(processed_img)
                text_result = pytesseract.image_to_string(pil_img, lang='eng', config='--psm 6')
                results[method] = len(text_result.strip())
                print(f"{method}: {results[method]} characters extracted")
            except Exception as e:
                print(f"{method}: Failed - {e}")
                results[method] = 0
        
        # Clean up
        if os.path.exists('test_pan_preprocessing.png'):
            os.remove('test_pan_preprocessing.png')
        
        # Check if PAN-specific method performed reasonably
        if results.get('pan_specific', 0) > 0:
            print("✓ PAN-specific preprocessing working")
            return True
        else:
            print("✗ PAN-specific preprocessing failed")
            return False
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def main():
    print("=" * 60)
    print("Testing ash-pan.jpg Recognition Fixes")
    print("=" * 60)
    
    tests = [
        test_improved_pan_recognition,
        test_pan_data_extraction,
        test_confidence_improvements,
        test_preprocessing_methods
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ PASSED")
            else:
                print("✗ FAILED")
        except Exception as e:
            print(f"✗ ERROR: {e}")
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{len(tests)} tests passed")
    
    if passed >= 3:
        print("🎉 ash-pan.jpg recognition fixes are working!")
        print("\nImprovements made:")
        print("- Added more flexible keywords (including 'govt', 'india')")
        print("- Lowered confidence threshold for PAN cards")
        print("- Added PAN number bonus in confidence calculation")
        print("- Added PAN-specific image preprocessing")
        print("- Improved pattern matching flexibility")
        print("\nThe system should now better recognize PAN cards like ash-pan.jpg!")
    else:
        print("⚠️  Some fixes need more work")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
