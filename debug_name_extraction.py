#!/usr/bin/env python3
"""
Debug name extraction specifically
"""

def debug_name_logic():
    """Debug the name extraction logic step by step"""
    print("Debugging name extraction logic...")
    
    sample_text = """INCOME TAX DEPARTMENT
GOVT. OF INDIA
PERMANENT ACCOUNT NUMBER

**********

ASHWAMEGH GAJANAN GANGASAGAR

Father's Name
GA<PERSON>AN<PERSON> GANGASAGAR

Date of Birth
16/11/1998

Signature"""
    
    lines = sample_text.split('\n')
    print("Lines analysis:")
    for i, line in enumerate(lines):
        print(f"Line {i}: '{line.strip()}'")
    
    print("\nStep-by-step name extraction:")
    
    # Simulate the extraction logic
    import re
    
    # Pattern 1: Explicit "Name:" label (updated pattern)
    print("\n1. Looking for explicit 'Name:' label...")
    name_with_label = re.search(r'(?:^|\n)\s*Name[:\s]*([A-Z][A-Z\s]+?)(?:\n|$)', sample_text, re.MULTILINE | re.IGNORECASE)
    if name_with_label:
        print(f"   Found: '{name_with_label.group(1)}'")
    else:
        print("   Not found")
    
    # Pattern 2: Standalone name lines
    print("\n2. Looking for standalone name lines...")
    pan_number_found = False
    father_section_found = False
    name_candidates = []
    
    for i, line in enumerate(lines):
        line = line.strip()
        print(f"   Processing line {i}: '{line}'")
        
        # Track when we find PAN number
        if re.match(r'^[A-Z]{5}[0-9]{4}[A-Z]$', line):
            pan_number_found = True
            print(f"     -> PAN number found!")
            continue
        
        # Track when we reach Father's section
        if 'father' in line.lower():
            father_section_found = True
            print(f"     -> Father's section found!")
            break
        
        # Look for name lines after PAN but before Father's section
        if pan_number_found and not father_section_found:
            print(f"     -> In name search zone")
            
            # Check if it looks like a name
            is_all_caps = re.match(r'^[A-Z][A-Z\s]+$', line)
            word_count = len(line.split()) if line else 0
            is_valid_length = len(line) >= 10
            
            print(f"        All caps: {bool(is_all_caps)}")
            print(f"        Word count: {word_count} (need 2-4)")
            print(f"        Length: {len(line)} (need >=10)")
            
            if (is_all_caps and 
                2 <= word_count <= 4 and 
                is_valid_length):
                print(f"        -> Potential name candidate: '{line}'")
                name_candidates.append(line)
                break  # Take the first valid name after PAN number
    
    print(f"\nName candidates found: {name_candidates}")
    
    # Test the actual function
    print(f"\nTesting actual function:")
    try:
        from document_processor import DocumentProcessor
        processor = DocumentProcessor()
        
        # Test the _is_valid_pan_name function
        test_names = [
            "ASHWAMEGH GAJANAN GANGASAGAR",
            "GAJANAN GANGASAGAR", 
            "INCOME TAX DEPARTMENT",
            "PERMANENT ACCOUNT NUMBER"
        ]
        
        print(f"Testing _is_valid_pan_name function:")
        for name in test_names:
            is_valid = processor._is_valid_pan_name(name)
            print(f"  '{name}' -> {is_valid}")
        
        # Test the actual extraction
        extracted_data = processor.extract_pan_data(sample_text)
        print(f"\nActual extraction result:")
        print(f"  Name: '{extracted_data.get('Name', 'NOT FOUND')}'")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

def main():
    debug_name_logic()

if __name__ == "__main__":
    main()
