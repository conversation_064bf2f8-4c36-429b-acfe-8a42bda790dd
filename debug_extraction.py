#!/usr/bin/env python3
"""
Debug data extraction issues
"""

def debug_pan_extraction():
    """Debug PAN data extraction step by step"""
    print("Debugging PAN data extraction...")
    
    # Simulate the actual text that might be extracted from ash-pan.jpg
    sample_text = """INCOME TAX DEPARTMENT
GOVT. OF INDIA
PERMANENT ACCOUNT NUMBER

**********

ASHWAMEGH GAJANAN GANGASAGAR

Father's Name
GAJANAN GANGASAGAR

Date of Birth
16/11/1998

Signature"""
    
    print("Sample text:")
    print("-" * 40)
    print(repr(sample_text))  # Show exact text with escape characters
    print("-" * 40)
    
    try:
        from document_processor import DocumentProcessor
        import re
        
        processor = DocumentProcessor()
        
        # Test each pattern individually
        print("\nTesting individual patterns:")
        
        # 1. PAN Number
        pan_pattern = r'([A-Z]{5}[0-9]{4}[A-Z])'
        pan_matches = re.findall(pan_pattern, sample_text)
        print(f"PAN Number pattern: {pan_pattern}")
        print(f"Matches: {pan_matches}")
        
        # 2. Name patterns
        name_patterns = [
            r'(?:Name)[:\s]*([A-Za-z\s]+?)(?:\n|Father)',
            r'^([A-Z][A-Z\s]+?)(?:\n.*(?:Father))',
            r'([A-Z]{2,}\s+[A-Z]{2,}\s+[A-Z]{2,})',  # All caps 3 words
            r'\n\s*([A-Z][A-Z\s]{10,})\s*\n',  # Standalone line with caps
        ]
        
        print(f"\nName patterns:")
        for i, pattern in enumerate(name_patterns, 1):
            matches = re.findall(pattern, sample_text, re.MULTILINE)
            print(f"Pattern {i}: {pattern}")
            print(f"Matches: {matches}")
        
        # 3. Father's name patterns
        father_patterns = [
            r'(?:Father[\'s]*\s*Name)[:\s]*([A-Z][A-Z\s]+?)(?:\n|$)',
            r'(?:Father)[\'s]*[:\s]*([A-Z][A-Z\s]+?)(?:\n|$)',
            r'Father[\'s]*\s*Name\s*\n\s*([A-Z][A-Z\s]+)',
        ]
        
        print(f"\nFather's name patterns:")
        for i, pattern in enumerate(father_patterns, 1):
            matches = re.findall(pattern, sample_text, re.MULTILINE | re.IGNORECASE)
            print(f"Pattern {i}: {pattern}")
            print(f"Matches: {matches}")
        
        # 4. Date of Birth patterns
        dob_patterns = [
            r'(?:DOB|Birth)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'Date\s*of\s*Birth\s*\n\s*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
        ]
        
        print(f"\nDate of Birth patterns:")
        for i, pattern in enumerate(dob_patterns, 1):
            matches = re.findall(pattern, sample_text, re.MULTILINE | re.IGNORECASE)
            print(f"Pattern {i}: {pattern}")
            print(f"Matches: {matches}")
        
        # Test the actual extraction method
        print(f"\nActual extraction method result:")
        extracted_data = processor.extract_pan_data(sample_text)
        for key, value in extracted_data.items():
            print(f"  {key}: '{value}'")
        
        return True
        
    except Exception as e:
        print(f"Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_line_by_line_analysis():
    """Analyze text line by line"""
    print("\n" + "="*50)
    print("Line by line analysis...")
    
    sample_text = """INCOME TAX DEPARTMENT
GOVT. OF INDIA
PERMANENT ACCOUNT NUMBER

**********

ASHWAMEGH GAJANAN GANGASAGAR

Father's Name
GAJANAN GANGASAGAR

Date of Birth
16/11/1998

Signature"""
    
    lines = sample_text.split('\n')
    print(f"Total lines: {len(lines)}")
    
    for i, line in enumerate(lines):
        print(f"Line {i}: '{line}' (length: {len(line)})")
        
        # Check what each line might be
        import re
        if re.match(r'[A-Z]{5}[0-9]{4}[A-Z]', line.strip()):
            print(f"  -> PAN Number detected")
        elif re.match(r'^[A-Z][A-Z\s]+$', line.strip()) and len(line.strip()) > 10:
            print(f"  -> Possible name (all caps)")
        elif re.match(r'\d{1,2}[/-]\d{1,2}[/-]\d{4}', line.strip()):
            print(f"  -> Date detected")
        elif 'father' in line.lower():
            print(f"  -> Father's name label")

def main():
    print("=" * 60)
    print("Debugging Data Extraction Issues")
    print("=" * 60)
    
    debug_pan_extraction()
    test_line_by_line_analysis()

if __name__ == "__main__":
    main()
