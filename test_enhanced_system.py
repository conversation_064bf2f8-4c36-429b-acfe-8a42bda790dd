#!/usr/bin/env python3
"""
Test script for the enhanced document processing system
"""

import os
from PIL import Image, ImageDraw

def test_multiple_preprocessing():
    """Test multiple preprocessing methods"""
    print("Testing Multiple Preprocessing Methods...")
    
    # Create a test image with poor quality simulation
    img = Image.new('RGB', (400, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    # Add some text with noise simulation
    text = """INCOME TAX DEPARTMENT
GOVT. OF INDIA
PERMANENT ACCOUNT NUMBER
**********
Name: RAJESH KUMAR SHARMA"""
    
    draw.text((20, 20), text, fill='black')
    
    # Add some noise (simulate poor quality)
    import random
    pixels = img.load()
    for i in range(img.size[0]):
        for j in range(img.size[1]):
            if random.random() < 0.05:  # 5% noise
                pixels[i, j] = (128, 128, 128)
    
    img.save('test_noisy_pan.png')
    
    try:
        from document_processor import DocumentProcessor
        
        processor = DocumentProcessor()
        
        # Test the enhanced extraction
        result = processor.process_document('test_noisy_pan.png', 'test_noisy_pan.png')
        
        print("Processing Result:")
        print(f"Document Type: {result.get('Document Type', 'Unknown')}")
        print(f"Text Length: {len(result.get('Raw Text', ''))}")
        
        if result.get('Document Type') == 'PAN Card':
            print("✓ Document type correctly identified")
            return True
        else:
            print("✗ Document type not identified correctly")
            return False
            
    except Exception as e:
        print(f"Test failed: {e}")
        return False
    finally:
        if os.path.exists('test_noisy_pan.png'):
            os.remove('test_noisy_pan.png')

def test_unknown_document_suggestions():
    """Test suggestions for unknown documents"""
    print("\nTesting Unknown Document Suggestions...")
    
    # Create a document with mixed content
    sample_text = """SOME UNKNOWN DOCUMENT
This document has some text
But also has ********** which looks like PAN
And 1234 5678 9012 which looks like Aadhaar
Random content here"""
    
    try:
        from document_processor import DocumentProcessor
        
        processor = DocumentProcessor()
        
        # Test suggestions
        suggestions = processor.get_document_suggestions(sample_text)
        
        print("Suggestions:")
        print(f"Possible types: {len(suggestions['possible_types'])}")
        print(f"Detected patterns: {suggestions['detected_patterns']}")
        
        # Check if it detected the patterns
        has_pan_pattern = any('PAN' in pattern for pattern in suggestions['detected_patterns'])
        has_aadhaar_pattern = any('12-digit' in pattern for pattern in suggestions['detected_patterns'])
        
        if has_pan_pattern and has_aadhaar_pattern:
            print("✓ Correctly detected PAN and Aadhaar patterns")
            return True
        else:
            print("✗ Failed to detect expected patterns")
            return False
            
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def test_document_type_detection():
    """Test improved document type detection"""
    print("\nTesting Improved Document Type Detection...")
    
    test_cases = [
        ("INCOME TAX DEPARTMENT GOVT OF INDIA PERMANENT ACCOUNT NUMBER **********", "PAN Card"),
        ("GOVERNMENT OF INDIA UNIQUE IDENTIFICATION AUTHORITY AADHAAR 1234 5678 9012", "Aadhaar Card"),
        ("DRIVING LICENSE DL1234567890 TRANSPORT DEPARTMENT", "Driving License"),
    ]
    
    try:
        from document_types import DocumentTypeManager
        
        manager = DocumentTypeManager()
        passed = 0
        
        for text, expected_type in test_cases:
            doc_type, confidence = manager.identify_document_type(text)
            print(f"Text: {text[:50]}...")
            print(f"Detected: {doc_type} (confidence: {confidence:.2f})")
            print(f"Expected: {expected_type}")
            
            if doc_type == expected_type:
                print("✓ CORRECT")
                passed += 1
            else:
                print("✗ INCORRECT")
            print()
        
        return passed >= 2
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def test_manual_classification():
    """Test manual classification functionality"""
    print("\nTesting Manual Classification...")
    
    # Simulate unknown document that gets manually classified
    sample_text = """Some unclear document
**********
RAJESH KUMAR SHARMA
Father: MOHAN LAL SHARMA"""
    
    try:
        from document_processor import DocumentProcessor
        
        processor = DocumentProcessor()
        
        # Manually extract as PAN card
        structured_data = processor.extract_structured_data(sample_text, 'PAN Card')
        
        print("Manual classification as PAN Card:")
        for key, value in structured_data.items():
            print(f"  {key}: {value}")
        
        # Check if we got the expected fields
        expected_fields = ['PAN Number', 'Name']
        found_fields = [field for field in expected_fields if field in structured_data]
        
        if len(found_fields) >= 1:
            print("✓ Manual classification working")
            return True
        else:
            print("✗ Manual classification failed")
            return False
            
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def test_confidence_thresholds():
    """Test if confidence thresholds are working properly"""
    print("\nTesting Confidence Thresholds...")
    
    try:
        from document_types import DocumentTypeManager
        
        manager = DocumentTypeManager()
        
        # Test with minimal content
        weak_text = "tax department"
        doc_type, confidence = manager.identify_document_type(weak_text)
        
        print(f"Weak text result: {doc_type} (confidence: {confidence:.2f})")
        
        # Test with strong content
        strong_text = "INCOME TAX DEPARTMENT PERMANENT ACCOUNT NUMBER **********"
        doc_type2, confidence2 = manager.identify_document_type(strong_text)
        
        print(f"Strong text result: {doc_type2} (confidence: {confidence2:.2f})")
        
        # Strong text should have higher confidence
        if confidence2 > confidence:
            print("✓ Confidence scoring working correctly")
            return True
        else:
            print("✗ Confidence scoring needs improvement")
            return False
            
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def main():
    print("=" * 60)
    print("Testing Enhanced Document Processing System")
    print("=" * 60)
    
    tests = [
        test_multiple_preprocessing,
        test_unknown_document_suggestions,
        test_document_type_detection,
        test_manual_classification,
        test_confidence_thresholds
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ PASSED")
            else:
                print("✗ FAILED")
        except Exception as e:
            print(f"✗ ERROR: {e}")
        print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{len(tests)} tests passed")
    
    if passed >= 4:
        print("🎉 Enhanced system is working excellently!")
        print("\nNew features available:")
        print("- Multiple image preprocessing methods")
        print("- Document type suggestions for unknown documents")
        print("- Manual classification options")
        print("- Improved confidence scoring")
        print("- Better pattern detection")
    else:
        print("⚠️  Some features need more work")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
